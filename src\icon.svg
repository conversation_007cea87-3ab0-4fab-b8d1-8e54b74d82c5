<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="160" viewBox="0 0 200 160">
  <defs>
    <clipPath id="clip-path">
      <rect id="矩形_18513" data-name="矩形 18513" width="7.801" height="8.949"/>
    </clipPath>
    <clipPath id="clip-池">
      <rect width="200" height="160"/>
    </clipPath>
  </defs>
  <g id="池" clip-path="url(#clip-池)">
    <g id="组_24357" data-name="组 24357" transform="translate(-998.703 -67.343)">
      <path id="路径_65864" data-name="路径 65864" d="M189.094,345.366c25.5,0,46.175,1.222,46.175,2.73s-20.674,2.73-46.175,2.73-46.175-1.222-46.175-2.73,20.674-2.73,46.175-2.73" transform="translate(916.957 -131.535)" fill="#dfecfb"/>
      <path id="Path" d="M0,8.442S.944,6.173,1.186,5.729.4,1.756.9,1.683s.93,2.158.93,2.158S3.01.162,3.493.007,5.434,2.371,5.248,3.622A11.537,11.537,0,0,1,3.086,7.505a15.751,15.751,0,0,0-1.249,2.72Z" transform="translate(1050.093 134)" fill="#f1b282"/>
      <path id="Path-2" data-name="Path" d="M0,7.923s1.346-2.018,1.683-2.4.867-2.546,1.371-2.5.1.906.1.906A17.022,17.022,0,0,1,5.457,2c.438-.139.563-.037.635.492a.641.641,0,0,1,.476.43c.348.017.388.563.388.563v.5a2.377,2.377,0,0,1-.18.649C6.313,5.817,4.2,7.289,3.614,7.751A25.138,25.138,0,0,0,1.553,9.979Z" transform="translate(1056.535 141.233)" fill="#ffdcc3"/>
      <g id="组_24344" data-name="组 24344" transform="translate(1129.033 163.71)">
        <g id="组_24344-2" data-name="组 24344" transform="translate(0 0)">
          <g id="Group" transform="translate(0 1.689)">
            <path id="Path-3" data-name="Path" d="M.616,50.231h0a.923.923,0,0,0,1.178-.564L18.883,1.233A.925.925,0,0,0,18.32.053h0a.923.923,0,0,0-1.178.564L.053,49.052A.925.925,0,0,0,.616,50.231Z" transform="translate(0 0)" fill="#dfefff"/>
            <path id="Path-4" data-name="Path" d="M.616,50.231h0a.923.923,0,0,0,1.178-.564L18.882,1.233a.925.925,0,0,0-.563-1.18h0a.923.923,0,0,0-1.178.564L.053,49.052A.925.925,0,0,0,.616,50.231Z" transform="translate(12.656 0)" fill="#dfefff"/>
            <rect id="Rectangle" width="13.44" height="1.671" transform="translate(29.341 6.473) rotate(180)" fill="#dfefff"/>
            <rect id="Rectangle-2" data-name="Rectangle" width="13.44" height="1.671" transform="translate(25.819 16.59) rotate(180)" fill="#dfefff"/>
            <rect id="Rectangle-3" data-name="Rectangle" width="13.44" height="1.671" transform="translate(22.297 26.708) rotate(180)" fill="#dfefff"/>
            <rect id="Rectangle-4" data-name="Rectangle" width="13.44" height="1.671" transform="translate(18.775 36.825) rotate(180)" fill="#dfefff"/>
            <rect id="Rectangle-5" data-name="Rectangle" width="13.44" height="1.671" transform="translate(15.253 46.943) rotate(180)" fill="#dfefff"/>
          </g>
          <g id="Group-2" data-name="Group" transform="translate(24.668 1.689)">
            <path id="Path-5" data-name="Path" d="M18.319,50.231h0a.923.923,0,0,1-1.178-.564L.053,1.233A.925.925,0,0,1,.616.053h0A.923.923,0,0,1,1.794.617L18.883,49.052A.925.925,0,0,1,18.319,50.231Z" transform="translate(12.656 0)" fill="#f3faff"/>
            <path id="Path-6" data-name="Path" d="M18.32,50.231h0a.923.923,0,0,1-1.178-.564L.053,1.233A.925.925,0,0,1,.616.053h0A.923.923,0,0,1,1.794.617L18.883,49.052A.924.924,0,0,1,18.32,50.231Z" transform="translate(0 0)" fill="#f3faff"/>
            <rect id="Rectangle-6" data-name="Rectangle" width="13.44" height="1.671" transform="translate(2.25 4.802)" fill="#f3faff"/>
            <rect id="Rectangle-7" data-name="Rectangle" width="13.44" height="1.671" transform="translate(5.772 14.919)" fill="#f3faff"/>
            <rect id="Rectangle-8" data-name="Rectangle" width="13.44" height="1.671" transform="translate(9.294 25.037)" fill="#f3faff"/>
            <rect id="Rectangle-9" data-name="Rectangle" width="13.44" height="1.671" transform="translate(12.817 35.154)" fill="#f3faff"/>
            <rect id="Rectangle-10" data-name="Rectangle" width="13.44" height="1.671" transform="translate(16.339 45.272)" fill="#f3faff"/>
          </g>
          <path id="Path-7" data-name="Path" d="M24.97,3.082H1.539A1.54,1.54,0,0,1,0,1.541H0A1.54,1.54,0,0,1,1.539,0H24.97a1.54,1.54,0,0,1,1.539,1.541h0A1.54,1.54,0,0,1,24.97,3.082Z" transform="translate(15.178)" fill="#c6e1fd"/>
        </g>
      </g>
      <path id="路径_65865" data-name="路径 65865" d="M0,2.329,2.612,6.994,5.776,4.634,2.26,0Z" transform="translate(1102.317 82.522) rotate(-25)" fill="#fac896"/>
      <path id="路径_65866" data-name="路径 65866" d="M0,.574.982,2.28,2.424.892C2.855.476,2.3-.161,1.682.037Z" transform="translate(1103.603 84.523) rotate(-25)" fill="#f39c55"/>
      <path id="路径_65867" data-name="路径 65867" d="M3.015,0A3.186,3.186,0,0,1,6.029,3.333,3.186,3.186,0,0,1,3.015,6.665,3.186,3.186,0,0,1,0,3.333,3.186,3.186,0,0,1,3.015,0" transform="translate(1098.777 80.966) rotate(-25)" fill="#fac896"/>
      <g id="组_24348" data-name="组 24348" transform="translate(1097.839 76.23) rotate(-14)">
        <g id="组_24346" data-name="组 24346" transform="translate(0 0)" clip-path="url(#clip-path)">
          <path id="路径_65862" data-name="路径 65862" d="M2.1,0A2.3,2.3,0,0,1,4.2,2.455,2.3,2.3,0,0,1,2.1,4.909,2.3,2.3,0,0,1,0,2.455,2.3,2.3,0,0,1,2.1,0" transform="translate(3.605 0)"/>
          <path id="路径_65863" data-name="路径 65863" d="M.418,3.535s.7-1.427,1.77-1.408a3.4,3.4,0,0,0,1.627,2.34c.342.19.238.542.314.609s.2.114.257-.4.58-.809.761-.571S4.68,5.7,4.69,5.856s.637.1.752.1,2.569-1.893,1-4.234S1.165-.476.209,1.579c-.257.685-.366,1.513.209,1.955" transform="translate(0 2.987)"/>
        </g>
      </g>
      <g id="组_24345" data-name="组 24345" transform="translate(1094.789 83.334)">
        <path id="Path-8" data-name="Path" d="M13.365,1.269S11.45,3.634,9.494,6.164C7.153,9.192,4.976,11.021,3.9,13.047c-.254.478.811,2.172.265,2.975S.959,17.943.455,16.26c-.345-1.153-.938-2.521.262-3.683C1.159,12.148,3.375,8.92,4.588,7.3S8.811.234,8.811.234,11.884-.735,13.365,1.269Z" transform="translate(3.556 9.612)" fill="#fac49c"/>
        <path id="Path-9" data-name="Path" d="M1.68,4.836c.1-1.029-.032-1.4-.186-1.521l-.064-.044h0L0,2.272,1.809,0,3.44,1.3c.063.05,1.5.284,1.182,1.009S1.447,7.2,1.68,4.836Z" transform="translate(26.479 33.915)"/>
        <path id="Path-10" data-name="Path" d="M10.493.052S.163,4.459,0,8.9s13.16,12.9,13.16,12.9l1.975-2.14s-4.288-7.044-9.218-9.238c2.2-1.8,8.732-2.327,8.732-8.057C14.649-.554,10.493.052,10.493.052Z" transform="translate(13.94 14.737)"/>
        <path id="Path-11" data-name="Path" d="M.465,2.91c.869-.561,1.073-.894,1.075-1.092l-.005-.078h0L1.426,0H4.331V2.084c0,.081.713,1.351-.054,1.553S-1.533,4.2.465,2.91Z" transform="translate(20.156 45.491)"/>
        <path id="Path-12" data-name="Path" d="M11.871.006S14.952.1,14.952,4.041c0,3.7-5.212,8.441-9.35,14.494,1,5.627-.912,14.418-.912,14.418H1.62S-.226,22.149.023,17.68C.3,12.764,3.289,7.4,4.93,3.563,5.921,1.247,9.74-.1,11.871.006Z" transform="translate(19.859 12.531)"/>
        <path id="Path-13" data-name="Path" d="M14.485,4.659S5.738-1.238,2.7.784C-.655,3.023-.412,7.361.9,9.878a3.857,3.857,0,0,0,1.659,1.8,16.546,16.546,0,0,0,3.56.588A5.191,5.191,0,0,1,10.155,15.5c2.534,5.128,1.32,6.66,2.073,4.724,1.2-3.087,14.361-8.541,14.361-8.541Z" transform="translate(11.239 -0.362)" fill="#00b8ff"/>
        <path id="Path-14" data-name="Path" d="M0,3.7A3.857,3.857,0,0,0,1.659,5.5a16.546,16.546,0,0,0,3.56.588C6.376,4.219,4.962.059,4.962.059,2.671-.382,1.023,1.713,0,3.7Z" transform="translate(12.14 5.817)" fill="#09a3ef"/>
        <path id="Path-15" data-name="Path" d="M14.951,1.679s-2.2,2.1-4.47,4.355c-2.713,2.7-5.107,4.235-6.433,6.105-.313.441.525,2.258-.121,2.984S.5,16.616.214,14.882c-.193-1.187-.605-2.621.735-3.619.494-.368,3.107-3.284,4.52-4.736s5.1-6.461,5.1-6.461S13.74-.5,14.951,1.679Z" transform="translate(0 9.359)" fill="#fac49c"/>
        <path id="Path-16" data-name="Path" d="M2.923.064C.878.683.855,5.545.087,6.566s3.8,3.184,4.235,2.8c1.015-.91,2.809-3.764,2.8-5.753S4.41-.386,2.923.064Z" transform="translate(10.238 2.496)" fill="#00abff"/>
        <path id="Path-17" data-name="Path" d="M1.851,2.365A18.757,18.757,0,0,1,1.816,4.94C1.589,6.1-.339,6.283.052,7.181c.365.838,3.2.489,4.24-.758C4.366,6.333,5.747,3.6,6.364,2.369a.818.818,0,0,0-.212-1L4.838.478a.538.538,0,0,0-.36-.093A.249.249,0,0,0,4.32.578a2.645,2.645,0,0,1-.52,1.17C3.308,2.423,2.012,2.154,1.851,2.365Z" transform="translate(25.855 34.992)" fill="#0b224e"/>
        <path id="Path-18" data-name="Path" d="M4.7.124A.433.433,0,0,0,4.145.08C3.6.469,2.515,1.238,2.5,1.244c-.687.3-2.538.808-2.5,1.512.049.9,5.13.6,7.11.4.4-.041.72-.609.7-1.012L7.75.507A.234.234,0,0,0,7.392.319a3.266,3.266,0,0,1-1.3.518A2.05,2.05,0,0,1,4.7.124Z" transform="translate(16.974 47.047)" fill="#0b224e"/>
      </g>
      <g id="拼图" transform="translate(1042.61 112.176)">
        <path id="路径_65555" data-name="路径 65555" d="M472.371,571.9a1.765,1.765,0,0,0,1.758-1.758V532.327a1.765,1.765,0,0,0-1.758-1.758H461.652a8.664,8.664,0,0,0-17.1,0h-9.992a1.766,1.766,0,0,0-1.758,1.758v37.813a1.766,1.766,0,0,0,1.758,1.758Z" transform="translate(-410.489 -467.513)" fill="#deeffa"/>
        <path id="路径_65558" data-name="路径 65558" d="M171.64,619.628a1.766,1.766,0,0,0,1.758-1.758V580.058a1.765,1.765,0,0,0-1.758-1.758H133.827a1.765,1.765,0,0,0-1.758,1.758v10.719a8.664,8.664,0,0,0,0,17.1v9.992a1.766,1.766,0,0,0,1.758,1.758Z" transform="translate(-69.051 -515.244)" fill="#003bc0"/>
        <path id="路径_65561" data-name="路径 65561" d="M145.741,318.884a8.672,8.672,0,0,0,8.551-7.269h9.992a1.766,1.766,0,0,0,1.758-1.758v-37.8a1.766,1.766,0,0,0-1.758-1.758H126.471a1.766,1.766,0,0,0-1.758,1.758v11.657a1.759,1.759,0,0,0,1.758,1.758h.079a.644.644,0,0,0,.159-.013h.119a5.881,5.881,0,1,1,0,11.763h-.119c-.053,0-.106-.013-.172-.013h-.079a1.759,1.759,0,0,0-1.758,1.758V309.9a1.766,1.766,0,0,0,1.758,1.758H137.19A8.661,8.661,0,0,0,145.741,318.884Z" transform="translate(-61.682 -247.951)" fill="#deeffa"/>
        <path id="路径_65564" data-name="路径 65564" d="M506.018,158.15a1.77,1.77,0,0,0,1.2-.476l8.5-7.97a1.732,1.732,0,0,0,.555-1.269,1.811,1.811,0,0,0-.529-1.282.848.848,0,0,0-.119-.106l-.079-.079a5.872,5.872,0,0,1,8.564-8.036.575.575,0,0,1,.079.093,1.156,1.156,0,0,1,.106.132,1.757,1.757,0,0,0,1.242.608h.093a1.77,1.77,0,0,0,1.2-.476l7.97-7.481a1.756,1.756,0,0,0,.079-2.485l-7.335-7.811a8.678,8.678,0,0,0-.555-11.208,8.681,8.681,0,0,0-11.155-1.269L509,101.755a1.758,1.758,0,0,0-2.485-.079l-27.57,25.865a1.756,1.756,0,0,0-.079,2.485l25.865,27.57a1.747,1.747,0,0,0,1.229.555Z" transform="translate(-478.394 -101.2)" fill="#ffd900"/>
      </g>
      <path id="路径_65565" data-name="路径 65565" d="M31.238,147.011s-3.8,2.443-3.593,8.955c-.021.945-9.049,20.155-9.237,20.646a4.418,4.418,0,0,0,2.252,1.3c.716,0,9.044-8.855,11.684-13.544.947-1.791,3.488-5.829,1.663-9.607s.228-8.272.228-8.272Z" transform="translate(997.595 25.058)" fill="#004ec0"/>
      <path id="路径_65567" data-name="路径 65567" d="M4.8,345.462c2.65,0,4.8.412,4.8.919s-2.148.919-4.8.919-4.8-.412-4.8-.919,2.148-.919,4.8-.919" transform="translate(1012.569 -136.803)" fill="#d4e5f7"/>
      <path id="路径_65568" data-name="路径 65568" d="M92.379,192.353s.879,6.394-1.08,9.478c0,0,3.186-4.687,2.816-8.009s-1.737-1.47-1.737-1.47" transform="translate(938.298 -11.832)" fill="#003ba9"/>
      <path id="路径_65569" data-name="路径 65569" d="M147.449,345.366c2.5,0,4.53.342,4.53.764s-2.028.764-4.53.764-4.53-.342-4.53-.764,2.028-.764,4.53-.764" transform="translate(896.307 -136.725)" fill="#d4e5f7"/>
      <path id="路径_65570" data-name="路径 65570" d="M93.515,148.744s-2.128,8.185,3.517,12.635c1,1,2.28,4.081,3.354,8.966s2.643,11.533,2.643,11.533a4.414,4.414,0,0,0,3.122-.3c.032-.1.781-14.622,0-17.715s-1.856-7.457-3.257-10.225c-.521-1.042-.847-5.367-.847-5.367Z" transform="translate(936.816 23.607)" fill="#004ec0"/>
      <path id="路径_65572" data-name="路径 65572" d="M79.023,152.165l-.894,1.227s5.127,2.062,7.586,2.176a25.238,25.238,0,0,0,4.6-.228l-.215-1.462Z" transform="translate(949.012 20.441)" fill="#155693"/>
      <path id="路径_65573" data-name="路径 65573" d="M159.854,31.922a2.864,2.864,0,0,1-.412-3.076.947.947,0,0,1,.268-.37,30.558,30.558,0,0,0,2.887-2.105c1.081-.933,4.7-5.5,5.829-6.848a2.611,2.611,0,0,1,1.035-.5,1.388,1.388,0,0,1,1.319,2.02,95.332,95.332,0,0,1-4.839,8.449c-.928,1.166-2.279,2.828-3.122,3.862a.971.971,0,0,1-.637.334,1.436,1.436,0,0,1-1.292-.522Z" transform="translate(883.087 128.754)" fill="#ffe69f"/>
      <path id="路径_65574" data-name="路径 65574" d="M146.2,326.173s2.764,0,3.138-.522a1.83,1.83,0,0,0,1.156,1.982c1.4.663,2.7.793,3.322,1.5.261.591-.228.716-2.149.782s-4.917.586-5.243-.521a9.777,9.777,0,0,1-.224-3.218" transform="translate(893.641 -120.687)" fill="#0b224e"/>
      <path id="路径_65575" data-name="路径 65575" d="M11,306.878a5.188,5.188,0,0,1,2.617,1.447s-1.155,1.543-.293,2.656c.816,1.054,1.921,2.034,1.983,2.622,0,.517-.593.724-2.145.046a13.277,13.277,0,0,1-3.672-2.03,1.188,1.188,0,0,1-.188-1.293c.5-1.164.819-1.689,1.7-3.448" transform="translate(1005.091 -105.416)" fill="#0b224e"/>
      <path id="路径_65576" data-name="路径 65576" d="M133.158,5.908h0a1.629,1.629,0,0,0-2.053-.392L121.1,11.765a1.867,1.867,0,0,0-.536,1.724,2.464,2.464,0,0,0,.537,1.526l.382.42a2.3,2.3,0,0,0,1.937.765,1.759,1.759,0,0,0,.789-.265,96.945,96.945,0,0,0,8.836-7.96,1.542,1.542,0,0,0,.113-2.066" transform="translate(914.508 139.905)" fill="#ffe69f"/>
      <path id="路径_65577" data-name="路径 65577" d="M136.346,42.505l.521,2.8-.283.265-1.72-3.148Z" transform="translate(902.86 109.714)" fill="#f39c55"/>
      <path id="路径_65578" data-name="路径 65578" d="M74.07,63.757,75.576,60.1a.746.746,0,0,1,1.3-.149l2.83,3.955a.746.746,0,0,0,.926.24l5.423-2.572a.746.746,0,0,1,.757.069,6.175,6.175,0,0,1,2.554,4.247.756.756,0,0,1-.179.585c-.8.918-3.683,4.151-4.443,5a.743.743,0,0,0-.188.486l-.075,6.086a.744.744,0,0,1-.278.572c-.845.679-3.8,2.27-11.2-.261a23.637,23.637,0,0,1-4.013-1.893.743.743,0,0,1-.257-.923Z" transform="translate(956.717 95.709)" fill="#ffd900"/>
      <path id="路径_65579" data-name="路径 65579" d="M106.653,62.287c.245.123.616-.092.805-.256a1.637,1.637,0,0,0,.441-.664l-2.262-3.161a1.518,1.518,0,0,0-.632.121l-.479,1.161a1.891,1.891,0,0,0,.526,1.1c.433.406.911.292,1.223.8.245.4.126.767.379.893" transform="translate(927.539 96.876)" fill="#e87c30"/>
      <path id="路径_65580" data-name="路径 65580" d="M145.933,124.229l-1.826,1.938,1.813-.969Z" transform="translate(895.34 43.167)" fill="#e87c30"/>
      <path id="路径_65584" data-name="路径 65584" d="M139.912,52.288a.073.073,0,0,1-.04-.133l.174-.115a.073.073,0,0,1,.08.121l-.174.115a.073.073,0,0,1-.04.012" transform="translate(898.811 101.902)" fill="#fff"/>
      <path id="路径_65585" data-name="路径 65585" d="M129.548,55.359a.073.073,0,0,1-.034-.137l1.281-.677a.073.073,0,0,1,.068.129l-1.281.677a.072.072,0,0,1-.034.008" transform="translate(907.242 99.86)" fill="#fff"/>
      <path id="路径_65595" data-name="路径 65595" d="M136.8,166.079l.138-.016a.344.344,0,0,1,.287-.042l.139-.025a.434.434,0,0,0-.564.083" transform="translate(901.281 9.246)" fill="#fff"/>
      <path id="路径_65598" data-name="路径 65598" d="M123.16,82.259a.44.44,0,1,0,.44.44.44.44,0,0,0-.44-.44m0,.8a.361.361,0,1,1,.361-.36A.361.361,0,0,1,123.16,83.06Z" transform="translate(912.739 77.309)" fill="#fff"/>
      <path id="路径_65609" data-name="路径 65609" d="M107.6,59.948a.439.439,0,0,0,.436-.4.7.7,0,0,0-.077-.029.36.36,0,0,1-.694.127.787.787,0,0,0-.062.055.439.439,0,0,0,.4.252" transform="translate(925.361 95.811)" fill="#fff"/>
      <path id="路径_65612" data-name="路径 65612" d="M165.729,109.422l.1-.114a.36.36,0,0,1,.179-.2l.1-.112a.439.439,0,0,0-.38.427" transform="translate(877.752 55.559)" fill="#fff"/>
      <path id="路径_65621" data-name="路径 65621" d="M115.406,71.259a1.694,1.694,0,0,0,.521-1.351c-.049-.879,2.784-.634,2.784-.634l.407.44-.326,2.165s-.326.993-2.052.634c-1.009-.325-1.449-.977-1.335-1.254" transform="translate(918.703 87.898)" fill="#fac896"/>
      <path id="路径_65622" data-name="路径 65622" d="M118.8,80.394l-.006.037s-.326.993-2.052.634a1.947,1.947,0,0,1-1.33-1.041l-.006.005c-.114.277.326.928,1.335,1.254,1.726.358,2.052-.634,2.052-.634l.039-.261-.034.006" transform="translate(918.703 79.126)" fill="#f39c55"/>
      <path id="路径_65623" data-name="路径 65623" d="M120.544,73.153,123.01,74.9l.263-1.749Z" transform="translate(914.508 84.716)" fill="#f39c55"/>
      <path id="路径_65624" data-name="路径 65624" d="M111.987,45.057s2.173,2.179,4.384,1.424.546-7.353-2.655-6.63-3.2,3.093-1.73,5.207" transform="translate(922.239 111.853)" fill="#fac896"/>
      <path id="路径_65625" data-name="路径 65625" d="M101.009,32.158a1.325,1.325,0,0,0,.154-.121c.332-.317.322-.877.519-1.281a2.4,2.4,0,0,1,.712-.881,2.858,2.858,0,0,1,3.2.271c.537.375.5.537,1.058.521s.993-.147,1.319.423a1.788,1.788,0,0,1-.586,2.052,2.3,2.3,0,0,0-1.172,2,4.531,4.531,0,0,0-.472,0s-.5-.619-.781-.472-.293.651.472,1.238c.488.375.342.733.342.733a2.066,2.066,0,0,1-1.146.986,2.218,2.218,0,0,1-1.427.268,3.05,3.05,0,0,1-2-1.221c-.57-.651-.065-.944-.749-1.791a1.119,1.119,0,0,1-.423-1.287,2.312,2.312,0,0,1,.936-1.405Z" transform="translate(931.25 120.236)"/>
      <path id="路径_65626" data-name="路径 65626" d="M130.409,42.938a.522.522,0,0,1-.173-.028l.017-.043a.483.483,0,0,0,.421-.059.523.523,0,0,0,.183-.226.357.357,0,0,1-.295-.1.22.22,0,0,1-.068-.205.186.186,0,0,1,.1-.153.189.189,0,0,1,.191.05.343.343,0,0,1,.14.283.379.379,0,0,1-.007.06,1.044,1.044,0,0,0,.229-.115.383.383,0,0,0,.112-.525l.04-.023a.434.434,0,0,1-.127.587,1,1,0,0,1-.267.127.56.56,0,0,1-.209.275.5.5,0,0,1-.293.092m.239-.774a.09.09,0,0,0-.033.006.142.142,0,0,0-.075.116.174.174,0,0,0,.051.162.309.309,0,0,0,.281.084.348.348,0,0,0,.011-.075.3.3,0,0,0-.123-.246.189.189,0,0,0-.113-.049" transform="translate(906.624 110.175)" fill="#231f20"/>
      <path id="路径_65627" data-name="路径 65627" d="M109.4,60.886a.731.731,0,0,1-.35-.105,2.18,2.18,0,0,1-.344-.224,1.08,1.08,0,0,1-.429-.122.933.933,0,0,1-.432-.416l.043-.019a.9.9,0,0,0,.41.393,1.083,1.083,0,0,0,.353.112.412.412,0,0,1-.135-.294.11.11,0,0,1,.054-.1.3.3,0,0,1,.276,0,.243.243,0,0,1,.138.212.2.2,0,0,1-.086.189.285.285,0,0,1-.12.04,2.435,2.435,0,0,0,.295.185c.431.237.589-.019.6-.029l.04.023a.36.36,0,0,1-.309.153m-.7-.76a.2.2,0,0,0-.1.026.064.064,0,0,0-.033.059.38.38,0,0,0,.162.3.278.278,0,0,0,.149-.036.153.153,0,0,0,.065-.147.2.2,0,0,0-.112-.172.283.283,0,0,0-.125-.029Z" transform="translate(924.84 95.416)" fill="#231f20"/>
      <rect id="矩形_18512" data-name="矩形 18512" width="41.452" height="66.272" transform="translate(1012.569 144.225)" fill="none"/>
    </g>
  </g>
</svg>
