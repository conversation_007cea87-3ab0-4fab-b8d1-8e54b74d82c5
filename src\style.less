html,
body {
  margin: 0;
  font-family: Helvetica, 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
}

* {
  box-sizing: border-box;
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background: rgba(187, 187, 187, 0.8);
  background-clip: padding-box;
  border: 2px solid transparent;
}

::-webkit-scrollbar-thumb:active,
::-webkit-scrollbar-thumb:hover {
  background: rgba(125, 125, 125, 0.8);
  background-clip: padding-box;
}

#app {
  height: 100%;
}

.yl-table {
  .ant-table-thead tr th {
    color: #757575 !important;
    font-weight: 700 !important;
    height: 34px !important;
    line-height: 34px !important;
    padding: 0px 0px 0px 6px !important;
    font-size: 13px !important;
    border-bottom: 1px solid #E1E1E1;
    border-right: 1px solid #E1E1E1;
  }

  .ant-table-tbody tr td {
    height: 34px !important;
    line-height: 34px !important;
    padding: 0px 0px 0px 6px !important;
    font-size: 13px !important;
    border-bottom: 1px solid #E1E1E1;
    border-right: 1px solid #E1E1E1;
  }

  .ant-table-measure-row {
    visibility: collapse;
  }

  .ant-table-expanded-row-fixed {
    margin: 0px -9px !important;
  }

  .anticon {
    font-size: 14px !important;
  }
}

.yl-modal {
  .ant-modal-body {
    height: 450px;
    overflow-y: auto;
  }

  .ant-checkbox-wrapper {
    height: 30px !important;
    line-height: 30px !important;
  }

  .ant-radio-wrapper {
    height: 30px !important;
    line-height: 30px !important;
  }
}

.yl-errorModai {
  .ant-modal-body {
    height: 200px;
    overflow-y: auto;
  }
}

.yl-errorNumber {
  .ant-input-number-input {
    color: red;
  }
}

.yl-guoci {
  margin: 16px 0px 0px 16px;
  background-color: #FFFFFF;
  float: left;
  padding: 10px 14px 10px 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.16);
  cursor: default;
  position: relative;

  &-detail {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 10px;
    right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.24);
    cursor: pointer;
  }

  &-order {
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 0;
    border-top: 30px solid #E1DEDE;
    border-right: 30px solid transparent;
  }

  &-orderText {
    position: absolute;
    width: 15px;
    height: 15px;
    left: 0;
    top: 0;
    color: #6C6B6B;
    text-align: center;
    line-height: 18px;
  }
}

.yl-cardTitle {
  font-size: 13px;
  text-align: initial;
  flex-shrink: 0;
  margin: 0px 8px 0px 0px;
  color: rgb(158, 158, 158);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 1.5;
}

.yl-cardText {
  font-size: 13px;
  text-align: initial;
  flex-shrink: 0;
  margin: 0px 8px 0px 0px;
  color: #333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: top;
  line-height: 1.5;
}

.yl-cardIcon {
  font-size: 20px;
  margin-right: 4px;
  font-weight: 400;
  font-family: 'MD_Icon' !important;
  color: #9A9A9A;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  text-align: left;
}

.yl-cardIconAdd {
  display: flex;
  color: #9A9A9A;
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 32px;
  background-color: rgb(255, 255, 255);
  position: fixed;
  left: 20px;
  z-index: 99;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 6px 0px rgba(0, 0, 0, 0.15);
}

.yl-cardIconBack {
  display: flex;
  color: #9A9A9A;
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 32px;
  background-color: rgb(255, 255, 255);
  position: fixed;
  right: 20px;
  z-index: 99;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 6px 0px rgba(0, 0, 0, 0.15);
}

.yl_modalApp {
  width: 350px !important;
  margin-left: calc(50% - 175px) !important;
  padding-top: 10px !important;

  .adm-modal-title {
    font-size: 16px !important;
    line-height: 30px !important;
    text-align: left !important;
  }

  .adm-modal-footer {
    height: 57px !important;
    padding: 8px 0 !important;
    display: block !important;

    .adm-space-item {
      width: 64px !important;
      height: 32px !important;
      margin: 0px !important;
      float: right !important;
      margin-right: 8px !important;

      .adm-modal-button {
        width: 64px !important;
        height: 32px !important;
        font-size: 14px !important;
        padding: 4px 15px 4px 15px !important;
        border: 1px solid rgb(217, 217, 217) !important;
      }
    }
  }

  .adm-selector-item {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.yl_formApp {
  .adm-list-item-content-prefix {
    width: 70px !important;
    line-height: 32px !important;
    padding: 8px 8px 8px 0px !important;
    font-size: 14px !important;
  }

  .adm-list-item-content-main {
    padding: 8px 0px 8px 0px !important;
    line-height: 32px !important;
    font-size: 14px !important;

    .ant-input {
      border: 1px solid rgb(217, 217, 217) !important;
      height: 32px !important;
    }
  }

  .adm-list-item-content {
    align-items: flex-end;
  }

  .adm-form-item-label {
    line-height: 20px !important;
    text-align: left !important;
    white-space: pre-wrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .adm-list-item-content-extra {
    padding-left: 8px !important;
    line-height: 32px !important;
    margin-bottom: 8px !important;
  }

  .adm-list-item-content-arrow {
    margin-left: 5px !important;
    margin-bottom: 14px !important;
  }
}