import React, { useEffect, useState, useLayoutEffect, useRef, useCallback } from "react";
import { env, config, api, utils, md_emitter } from "mdye";
import { ConfigProvider, Button, Input, Select, Popconfirm, Tooltip, message, Space } from 'antd';
import { PlusOutlined, RollbackOutlined, DeleteOutlined, SwapRightOutlined, EyeOutlined, FormOutlined, CopyOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Modal, Toast, DatePicker, Form, Cascader, Dialog, Picker, Selector, TextArea, Popup } from 'antd-mobile';
import zhCN from 'antd/es/locale/zh_CN';
import dayjs from 'dayjs';
import axios from 'axios';
import "./style.less";
import moment from 'moment';
import 'moment/locale/zh-cn';
moment.locale('zh-cn');
import Icon from '@ant-design/icons';
import { values } from "lodash";
import { calc } from "antd/es/theme/internal";
const ReturnSvg = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7.50016 11.6668L3.3335 7.50016L7.50016 3.3335" stroke="#9A9A9A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    <path d="M16.6668 16.6667V10.8333C16.6668 9.94928 16.3156 9.10143 15.6905 8.47631C15.0654 7.85119 14.2176 7.5 13.3335 7.5H3.3335" stroke="#9A9A9A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
);
const ReturnIcon = (props) => <Icon component={ReturnSvg} {...props} />;

const { getFilterRows, getRowRelationRows, addWorksheetRow, updateWorksheetRow, deleteWorksheetRow } = api;
const { openRecordInfo } = utils;

const timeColumns = [
  [
    { label: '00', value: '00' },
    { label: '01', value: '01' },
    { label: '02', value: '02' },
    { label: '03', value: '03' },
    { label: '04', value: '04' },
    { label: '05', value: '05' },
    { label: '06', value: '06' },
    { label: '07', value: '07' },
    { label: '08', value: '08' },
    { label: '09', value: '09' },
    { label: '10', value: '10' },
    { label: '11', value: '11' },
    { label: '12', value: '12' },
    { label: '13', value: '13' },
    { label: '14', value: '14' },
    { label: '15', value: '15' },
    { label: '16', value: '16' },
    { label: '17', value: '17' },
    { label: '18', value: '18' },
    { label: '19', value: '19' },
    { label: '20', value: '20' },
    { label: '21', value: '21' },
    { label: '22', value: '22' },
    { label: '23', value: '23' }
  ],
  [
    { label: '00', value: '00' },
    { label: '01', value: '01' },
    { label: '02', value: '02' },
    { label: '03', value: '03' },
    { label: '04', value: '04' },
    { label: '05', value: '05' },
    { label: '06', value: '06' },
    { label: '07', value: '07' },
    { label: '08', value: '08' },
    { label: '09', value: '09' },
    { label: '10', value: '10' },
    { label: '11', value: '11' },
    { label: '12', value: '12' },
    { label: '13', value: '13' },
    { label: '14', value: '14' },
    { label: '15', value: '15' },
    { label: '16', value: '16' },
    { label: '17', value: '17' },
    { label: '18', value: '18' },
    { label: '19', value: '19' },
    { label: '20', value: '20' },
    { label: '21', value: '21' },
    { label: '22', value: '22' },
    { label: '23', value: '23' },
    { label: '24', value: '24' },
    { label: '25', value: '25' },
    { label: '26', value: '26' },
    { label: '27', value: '27' },
    { label: '28', value: '28' },
    { label: '29', value: '29' },
    { label: '30', value: '30' },
    { label: '31', value: '31' },
    { label: '32', value: '32' },
    { label: '33', value: '33' },
    { label: '34', value: '34' },
    { label: '35', value: '35' },
    { label: '36', value: '36' },
    { label: '37', value: '37' },
    { label: '38', value: '38' },
    { label: '39', value: '39' },
    { label: '40', value: '40' },
    { label: '41', value: '41' },
    { label: '42', value: '42' },
    { label: '43', value: '43' },
    { label: '44', value: '44' },
    { label: '45', value: '45' },
    { label: '46', value: '46' },
    { label: '47', value: '47' },
    { label: '48', value: '48' },
    { label: '49', value: '49' },
    { label: '50', value: '50' },
    { label: '51', value: '51' },
    { label: '52', value: '52' },
    { label: '53', value: '53' },
    { label: '54', value: '54' },
    { label: '55', value: '55' },
    { label: '56', value: '56' },
    { label: '57', value: '57' },
    { label: '58', value: '58' },
    { label: '59', value: '59' }
  ]
];

export default function () {
  const { appId, worksheetId, viewId, controls, query } = config;
  const [filters, setFilters] = useState(config.filters || {});
  const [messageApi, contextHolder] = message.useMessage();

  const [filterData, setFilterData] = useState({});
  const [titleText, setTitleText] = useState([]);
  const [titleValue, setTitleValue] = useState([]);
  const [formCol, setFormCol] = useState([]);
  const [formData, setFormData] = useState([]);
  const [subTable, setSubTable] = useState([]);
  const [colValue, setColValue] = useState({});
  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [addOrUpdate, setAddOrUpdate] = useState('新建记录');
  const [updateRowId, setUpdateRowId] = useState('');
  //判定结果值
  const [result, setResult] = useState({});
  const [below, setBelow] = useState({});
  const [belowBak, setBelowBak] = useState({});
  const [maskVisible, setMaskVisible] = useState(false);
  const [belowModal, setBelowModal] = useState([]);
  //是否纠偏
  const [isPutPight, setIsPutPight] = useState(false);

  //表头字段
  const [headerField, setHeaderField] = useState('');
  //获取方式字段
  const [valueType, setValueType] = useState('');
  //下拉文本信息字段
  const [selectValue, setSelectValue] = useState('');
  //复选框文本信息字段
  const [checkValue, setCheckValue] = useState('');
  //复选框类型字段
  const [checkType, setCheckType] = useState('');
  //默认时间字段
  const [defaultTime, setDefaultTime] = useState('');
  //设备点位字段
  const [tag, setTag] = useState('');
  //获取api数据
  const [apiUrl, setApiUrl] = useState('');
  //api参数
  const [apiParam, setApiParam] = useState('');
  //上限
  const [topLimit, setTopLimit] = useState('');
  //下限
  const [lowerLimit, setLowerLimit] = useState('');
  //小数位数
  const [decimalPlaces, setDecimalPlaces] = useState(null);
  //执行表ID
  const [valueTable, setValueTable] = useState('');
  //配置主表关联字段
  const [valueLink, setValueLink] = useState('');
  //执行表操作人字段
  const [valueUser, setValueUser] = useState('');
  //执行表创建时间字段
  const [valueCTime, setValueCTime] = useState('');
  //执行表修改时间字段
  const [valueMTime, setValueMTime] = useState('');
  //执行表明细字段
  const [valueField, setValueField] = useState('');
  //执行表判定结果字段
  const [valueResult, setValueResult] = useState('');
  //执行表纠偏措施字段
  const [valueWrong, setValueWrong] = useState('');
  //逻辑删除字段
  const [deleteFlag, setDeleteFlag] = useState('');

  //计算公式参数-表单名称
  const [formName, setFormName] = useState('');
  //计算公式参数-任务ID
  const [taskId, setTaskId] = useState('');
  //一键获取设备数据按钮
  const [oneClickGet, setOneClickGet] = useState(false);
  const [oneClickLoading, setOneClickLoading] = useState(false);

  async function getColValue (dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, valueResult, dataWrong) {
    const res = await getFilterRows({
      worksheetId: dataSourse,
      pageIndex: 1,
      pageSize: 99999999,
      sortId: 'ctime',
      fastFilters: [{
        controlId: dataLink,
        dataType: 29,
        dateRange: 0,
        filterType: 24,
        maxValue: "",
        minValue: "",
        spliceType: 1,
        value: '',
        values: [query['id']]
      }, {
        controlId: dataFlag,
        dataType: 6,
        dateRange: 0,
        filterType: 2,
        maxValue: "",
        minValue: "",
        spliceType: 1,
        value: '',
        values: ['1']
      }]
    });
    let newColData = [];
    for (let i = 0; i < res.data.length; i++) {
      newColData[i] = JSON.parse(res.data[i][dataValue]);
      newColData[i]['rowid'] = res.data[i].rowid;
      newColData[i]['user'] = res.data[i][dataUser];
      newColData[i]['time'] = res.data[i][dataMTime];
      newColData[i][valueResult] = res.data[i][valueResult];
      newColData[i][dataWrong] = res.data[i][dataWrong];
    }
    setFormData(newColData);
  }

  async function addColValue () {
    let receiveControl = [{
      controlId: valueField,
      value: JSON.stringify(colValue)
    }, {
      controlId: valueLink,
      value: JSON.stringify([{
        sid: query['id']
      }])
    }, {
      controlId: valueUser,
      value: config.currentAccount.fullname
    }, {
      controlId: valueCTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }, {
      controlId: valueMTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }, {
      controlId: deleteFlag,
      value: 1
    }, {
      controlId: valueResult,
      value: JSON.stringify(result) != '{}' ? JSON.stringify(result) : ''
    },
    {
      controlId: valueWrong,
      value: JSON.stringify(below) != '{}' ? JSON.stringify(below) : ''
    }];
    //如果纠偏不合格不让保存
    if (isPutPight) {
      const isError = Object.keys(result).some(key => result[key] === '不合格')
      if (isError) {
        Toast.show({
          icon: 'error',
          content: '纠偏依然存在不合格'
        });
        return true;
      }
    }
    const res = await addWorksheetRow({
      worksheetId: valueTable,
      appId,
      receiveControls: receiveControl
    });
    getColValue(valueTable, valueLink, valueField, deleteFlag, valueUser, valueMTime, valueResult, valueWrong);
  }

  async function updateColValue () {
    let receiveControl = [{
      controlId: valueField,
      value: JSON.stringify(colValue)
    }, {
      controlId: valueMTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }, {
      controlId: valueResult,
      value: JSON.stringify(result) != '{}' ? JSON.stringify(result) : ''
    },
    {
      controlId: valueWrong,
      value: JSON.stringify(below) != '{}' ? JSON.stringify(below) : ''
    }];
    const res2 = await updateWorksheetRow({
      appId,
      worksheetId: valueTable,
      rowId: updateRowId,
      newOldControl: receiveControl
    });
    getColValue(valueTable, valueLink, valueField, deleteFlag, valueUser, valueMTime, valueResult, valueWrong);
  }

  async function deleteColValue (record) {
    let receiveControl = [{
      controlId: deleteFlag,
      value: 0
    }, {
      controlId: valueMTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }];
    const res2 = await updateWorksheetRow({
      appId,
      worksheetId: valueTable,
      rowId: record['rowid'],
      newOldControl: receiveControl
    });
    getColValue(valueTable, valueLink, valueField, deleteFlag, valueUser, valueMTime, valueResult, valueWrong);
  }

  async function loadRecords () {
    axios.post(env.url, {
      appKey: env.appKey,
      sign: env.sign
    })
      .then(function (response) {
        let s = {};
        for (let i = 0; i < response.data.data.length; i++) {
          s[response.data.data[i].name] = {};
          for (let j = 0; j < response.data.data[i].options.length; j++) {
            s[response.data.data[i].name][response.data.data[i].options[j].key] = response.data.data[i].options[j].value;
          }
        }
        setFilterData(s);
      })
      .catch(function (error) {
        console.log(error);
      })

    const res1 = await getFilterRows({
      worksheetId,
      viewId,
      pageIndex: 1,
      pageSize: 99999999,
      ...filters,
      fastFilters: [{
        controlId: 'rowid',
        dataType: 2,
        dateRange: 0,
        filterType: 1,
        maxValue: "",
        minValue: "",
        spliceType: 1,
        value: '',
        values: [query['id']]
      }]
    });
    const mainCols = env.visibleFields.split("，");
    setTitleText(mainCols);
    const fields = config.controls;
    let mainList = [];
    mainCols.forEach(element => {
      for (let i = 0; i < fields.length; i++) {
        if (fields[i].controlName == element) {
          mainList.push(res1.data[0][fields[i].controlId]);
          break;
        }
      }
    });
    setTitleValue(mainList);
    setFormName(res1.data[0][env.formName[0]]);
    setTaskId(res1.data[0][env.taskId[0]]);

    let headField = '';
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].controlId == env.subTable[0]) {
        for (let j = 0; j < fields[i].relationControls.length; j++) {
          if (fields[i].relationControls[j].controlName == env.headerField) {
            setHeaderField(fields[i].relationControls[j].controlId);
            headField = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueType) {
            setValueType(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.selectValue) {
            setSelectValue(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.tag) {
            setTag(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.apiUrl) {
            setApiUrl(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.apiParam) {
            setApiParam(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.defaultTime) {
            setDefaultTime(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.topLimit) {
            setTopLimit(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.lowerLimit) {
            setLowerLimit(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.checkValue) {
            setCheckValue(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.checkType) {
            setCheckType(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.decimalPlaces) {
            setDecimalPlaces(fields[i].relationControls[j].controlId);
          }
        }
        break;
      }
    }

    let dataSourse = '';
    let dataValue = '';
    let dataFlag = '';
    let dataLink = '';
    let dataUser = '';
    let dataCtime = '';
    let dataMTime = '';
    let dataWrong = '';
    let dataResult = '';
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].controlId == env.valueTable[0]) {
        setValueTable(fields[i].dataSource);
        dataSourse = fields[i].dataSource;
        for (let j = 0; j < fields[i].relationControls.length; j++) {
          if (fields[i].relationControls[j].controlName == env.valueLink) {
            setValueLink(fields[i].relationControls[j].controlId);
            dataLink = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueField) {
            setValueField(fields[i].relationControls[j].controlId);
            dataValue = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.deleteFlag) {
            setDeleteFlag(fields[i].relationControls[j].controlId);
            dataFlag = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueUser) {
            setValueUser(fields[i].relationControls[j].controlId);
            dataUser = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueCTime) {
            setValueCTime(fields[i].relationControls[j].controlId);
            dataCtime = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueMTime) {
            setValueMTime(fields[i].relationControls[j].controlId);
            dataMTime = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueWrong) {
            setValueWrong(fields[i].relationControls[j].controlId);
            dataWrong = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueResult) {
            setValueResult(fields[i].relationControls[j].controlId);
            dataResult = fields[i].relationControls[j].controlId;
          }
        }
        break;
      }
    }

    const res = await getRowRelationRows({
      controlId: env.subTable[0],
      rowId: query['id'],
      worksheetId,
      pageIndex: 1,
      pageSize: 99999999,
    });
    setSubTable(res.data);
    let newColumns = [];
    let defaultData = {};
    for (let i = 0; i < res.data.length; i++) {
      newColumns.push(res.data[i][headField]);
      defaultData[res.data[i][headField]] = '';
    }
    setFormCol(newColumns);
    setColValue(defaultData);
    getColValue(dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, dataResult, dataWrong);
  }

  useEffect(() => {
    loadRecords();
  }, []);
  //更新一键获取的状态
  useEffect(() => {
    subTable.map((item, i) => {
      if(filterData['获取方式'][item[valueType].substring(2, 38)] == '设备获取'){    
        setOneClickGet(true)
      }
    })
  }, [subTable]); 

  const showAddModal = () => {
    for (let key of Object.keys(colValue)) {
      colValue[key] = '';
    }
    let newData = JSON.parse(JSON.stringify(colValue));
    setBelow({});
    setBelowBak({});
    setColValue(newData);
    setAddOrUpdate('新建记录');
    setOpen(true);
  };
  //返回历史路由地址
  const historyBack = () => {
    history.back()
  };

  const showUpdateModal = (record) => {
    let newData = {};
    for (let key of Object.keys(record)) {
      if (key != 'rowid' || key != valueWrong) {
        newData[key] = record[key]
      }
    }
    if (record[valueResult]) {
      setResult(JSON.parse(record[valueResult]));
    }
    if (record[valueWrong]) {
      setBelow(JSON.parse(record[valueWrong]));
      setBelowBak(JSON.parse(record[valueWrong]));
    } else {
      setBelow({});
      setBelowBak({});
    }
    setColValue(newData);
    setUpdateRowId(record.rowid);
    setAddOrUpdate('修改记录');
    setOpen(true);
  };
  //复制并修改
  const copyAndUpdate = (record) => {
    let newData = {};
    for (let key of Object.keys(record)) {
      if (key != 'rowid' || key != valueWrong) {
        newData[key] = record[key]
      }
    }
    if (record[valueResult]) {
      setResult(JSON.parse(record[valueResult]));
    }
    if (record[valueWrong]) {
      setBelow(JSON.parse(record[valueWrong]));
      setBelowBak(JSON.parse(record[valueWrong]));
    } else {
      setBelow({});
      setBelowBak({});
    }
    setColValue(newData);
    //setUpdateRowId(record.rowid);
    setAddOrUpdate('复制记录');
    setOpen(true);
  };
  //纠偏方法
  const putRight = (record) => {
    setIsPutPight(true)
    let newData = {};
    for (let key of Object.keys(record)) {
      if ((key != 'rowid' || key != valueWrong)) {
        newData[key] = record[key]
      }
    }
    if (record[valueResult]) {
      setResult(JSON.parse(record[valueResult]));
    }
    if (record[valueWrong]) {
      setBelow(JSON.parse(record[valueWrong]));
      setBelowBak(JSON.parse(record[valueWrong]));
    } else {
      setBelow({});
      setBelowBak({});
    }
    setColValue(newData);
    //setUpdateRowId(record.rowid);
    setAddOrUpdate('纠偏措施');
    setOpen(true);
  };

  const handleOk = () => {
    setConfirmLoading(true);
    if (addOrUpdate === '新建记录' || addOrUpdate === '复制记录' || addOrUpdate === '纠偏措施') {
      const myPromise = addColValue();
      myPromise.then((value) => {
        if (value != true) {
          setConfirmLoading(false);
          setIsPutPight(false)
          setOpen(false);
        }
      })
    } else {
      updateColValue();
      setConfirmLoading(false);
      setIsPutPight(false)
      setOpen(false);
    }

  };
  //新增并继续
  const addAndContinue = () => {
    setConfirmLoading(true);
    addColValue();
    setConfirmLoading(false);
    Toast.show({
      icon: 'success',
      content: '保存成功'
    });
  };
  const handleCancel = () => {
    setIsPutPight(false)
    setOpen(false);
  };

  const getSelectOption = (options) => {
    let result = [];
    const list = options.split("；");
    list.map(item => {
      result.push({
        value: item,
        label: item
      });
    })
    return result;
  };
  /**
   * 一键获取方法
   */
  const handleGetOneClick = async () => {  
    setOneClickLoading(true)
    const promises = subTable.map((item,i) => {  
      if (filterData['获取方式'][item[valueType].substring(2, 38)] === '设备获取') {  
        //const title = formCol[i+1].title; // 请确保这里获取的 title 是正确的  
        const decimalPlace = item[decimalPlaces];  
        return getOneDataByTag(item, formCol[i], decimalPlace);  
      }  
      return Promise.resolve(null); // 如果不满足条件，返回 null  
    });
    const results = await Promise.all(promises);  
    const newData = { ...colValue };  
    const newBelow = { ...below };  
    const newResult = { ...result }; // 现有结果状态的拷贝  
    results.forEach(result => {  
      if (result) {  
        const { name, tempValue, judge } = result;  
  
        // 更新新的数据  
        newData[name] = tempValue;  
  
        // 更新结果状态  
        newResult[name] = judge ? '不合格' : '合格';  
  
        // 更新 below 状态  
        if (judge) {  
          newBelow[name] = belowBak[name] || '';  
        } else {  
          delete newBelow[name];  
        }  
      }  
    });  
    // 一次性更新状态  
    setColValue(newData);  
    setResult(newResult);  
    setBelow(newBelow);  
    console.log('所有设备数据已成功获取和更新！');  
    setOneClickLoading(false)
  };
  //一键获取设备数据
  const getOneDataByTag = async (item, name, decimalPlaces) => {  
    const max = item[topLimit];  
    const min = item[lowerLimit];  
    try {  
      const response = await axios.get(env.tagUrl, { params: { tag: item[tag] } });  
      let tempValue = response.data.value;  
      const judge = (min && parseFloat(tempValue) < parseFloat(min)) || (max && parseFloat(tempValue) > parseFloat(max));
      // 处理小数位  
      if (decimalPlaces && !isNaN(tempValue)) {  
        const count = (tempValue.toString().split('.')[1] || []).length;  
        if (count > decimalPlaces) {  
          tempValue = parseFloat(tempValue).toFixed(decimalPlaces);  
        }  
      }  
      return { name, tempValue, judge }; // 返回处理后的结果  
    } catch (error) {  
      console.error('获取数据时发生错误:', error);  
      return null; // 返回 null 表示发生了错误  
    }  
  };  

  const getDataByTag = (item, name, decimalPlaces) => {
    const max = item[topLimit];
    const min = item[lowerLimit];
    axios.get(env.tagUrl, {
      params: {
        tag: item[tag]
      }
    })
      .then(function (response) {
        let newData = JSON.parse(JSON.stringify(colValue));
        let tempValue = response.data.value;
        const judge = (min && parseFloat(tempValue) < parseFloat(min)) || (max && parseFloat(tempValue) > parseFloat(max))
        //新增判定结果值
        let parseResult = JSON.parse(JSON.stringify(result));
        judge ? parseResult[name] = '不合格' : parseResult[name] = '合格'
        setResult(parseResult)
        if (decimalPlaces) {
          if (!isNaN(Number(tempValue))) {
            const count = tempValue.toString().split('.')[1].length
            let parseValue = parseFloat(tempValue)
            if (parseValue % 1 != 0 && count > decimalPlaces) {
              parseValue = parseValue.toFixed(decimalPlaces)
              tempValue = parseValue
            }
          }
        }
        if (judge) {
          if (!(name in below)) {
            let newdata = JSON.parse(JSON.stringify(below));
            let newdatabak = JSON.parse(JSON.stringify(belowBak));
            newdata[name] = newdatabak[name] ? newdatabak[name] : '';
            setBelow(newdata);
          }
        } else {
          let newdata = JSON.parse(JSON.stringify(below));
          delete newdata[name];
          setBelow(newdata);
        }
        newData[name] = tempValue
        setColValue(newData);
      })
      .catch(function (error) {
        console.log(error);
      })
  }

  const getDataByApi = (url, param, name) => {
    axios.get(url, {
      params: JSON.parse(param)
    })
      .then(function (response) {
        let newData = JSON.parse(JSON.stringify(colValue));
        newData[name] = response.data.value;
        setColValue(newData);
      })
      .catch(function (error) {
        console.log(error);
      })
  }

  const getCount = (name) => {
    axios.get(env.formula, {
      params: {
        x: colValue[name],
        fieldName: name,
        formName: formName,
        taskId: taskId
      }
    })
      .then(function (response) {
        let newData = JSON.parse(JSON.stringify(colValue));
        newData[name] = response.data.y;
        setColValue(newData);
      })
      .catch(function (error) {
        console.log(error);
      })
  }

  const changeColValue = (name, value) => {
    let newData = JSON.parse(JSON.stringify(colValue));
    newData[name] = value;
    setColValue(newData);
  }

  const changeColValueByNumber = (item, name, value) => {
    const max = item[topLimit];
    const min = item[lowerLimit];
    const judge = (min && parseFloat(value) < parseFloat(min)) || (max && parseFloat(value) > parseFloat(max))
    //新增判定结果值
    let parseResult = JSON.parse(JSON.stringify(result));
    judge ? parseResult[name] = '不合格' : parseResult[name] = '合格'
    setResult(parseResult)
    if (!isPutPight) {
      if (judge) {
        if (!(name in below)) {
          let newdata = JSON.parse(JSON.stringify(below));
          let newdatabak = JSON.parse(JSON.stringify(belowBak));
          newdata[name] = newdatabak[name] ? newdatabak[name] : '';
          setBelow(newdata);
        }
      } else {
        let newdata = JSON.parse(JSON.stringify(below));
        delete newdata[name];
        setBelow(newdata);
      }
    } else {
      if (!(name in below)) {
        let newdata = JSON.parse(JSON.stringify(below));
        let newdatabak = JSON.parse(JSON.stringify(belowBak));
        newdata[name] = newdatabak[name] ? newdatabak[name] : '';
        setBelow(newdata);
      }
    }
    let newData = JSON.parse(JSON.stringify(colValue));
    newData[name] = value;
    setColValue(newData);
  }
  //数字输入框设备获取输入框保留两位小数
  const reserveDecimal = (item, name, value) => {
    if (value) {
      const digit = item[decimalPlaces];
      const count = value.toString().split('.')[1].length
      if (!isNaN(Number(value)) && digit) {
        let parseValue = parseFloat(value)
        if (parseValue % 1 != 0 && count > digit) {
          parseValue = parseValue.toFixed(digit)
          let newData = JSON.parse(JSON.stringify(colValue));
          newData[name] = parseValue;
          setColValue(newData);
        }
      }
    }
  }

  const changeCheckValue = (name, value, singleChoice) => {
    let newData = JSON.parse(JSON.stringify(colValue));
    newData[name] = value.join("，");
    setColValue(newData);
    //单选
    if (singleChoice) {
      //判定结果值
      let parseResult = JSON.parse(JSON.stringify(result));
      if (value == 'NG') {
        parseResult[name] = '不合格'
        setResult(parseResult)
      } else if (value == 'OK') {
        parseResult[name] = '合格'
        setResult(parseResult)
      }
      else {
        delete parseResult[name];
      }
      //是否是纠偏
      if (!isPutPight) {
        if (value == 'NG') {
          if (!(name in below)) {
            let newdata = JSON.parse(JSON.stringify(below));
            let newdatabak = JSON.parse(JSON.stringify(belowBak));
            newdata[name] = newdatabak[name] ? newdatabak[name] : '';
            setBelow(newdata);
          }
        } else {
          let newdata = JSON.parse(JSON.stringify(below));
          delete newdata[name];
          setBelow(newdata);
        }
      } else {
        if (!(name in below)) {
          let newdata = JSON.parse(JSON.stringify(below));
          let newdatabak = JSON.parse(JSON.stringify(belowBak));
          newdata[name] = newdatabak[name] ? newdatabak[name] : '';
          setBelow(newdata);
        }
      }
    }
  }

  const getDefaultTime = (type, item, name) => {
    let newData = JSON.parse(JSON.stringify(colValue));
    if (filterData['是否默认当前时间'][item[defaultTime].substring(2, 38)] == '是') {
      let value = '';
      if (type == '日期选择') {
        value = dayjs().format('YYYY-MM-DD');
      } else {
        value = dayjs().format('HH:mm');
      }
      newData[name] = value;
      setColValue(newData);
      return value;
    } else {
      return null;
    }
  }
  //时间段类型赋值
  const changeTimeColValue = (type, name, value) => {
    let newData = JSON.parse(JSON.stringify(colValue));
    newData[name] = value;
    setColValue(newData);
  }
  // 将timeString转换为TimePicker需要的value格式
  const parseTimeStringToValue = (type, timeString) => {
    if (!timeString) return null;
    const [startTimeStr, endTimeStr] = timeString.split(' - ');
    if (type == 'start') {
      return startTimeStr
    } else {
      return endTimeStr
    }
  }

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ width: '100%', height: '100%' }}>
        <div style={{ width: '100%', height: (titleText.length * 25 + 5) + 'px', backgroundColor: '#FFFFFF', paddingTop: '5px' }}>
          {
            titleText.map((item, i) => {
              return (
                <div style={{ width: '100%', height: '20px', lineHeight: '20px', fontSize: '14px', paddingLeft: '16px', marginBottom: '5px', display: 'flex' }}>
                  <div style={{ color: '#757575', lineHeight: '20px' }}>
                    {item}：
                  </div>
                  <div style={{
                    color: '#333333', fontWeight: 'bold', lineHeight: '20px',
                    overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis', maxWidth: 'calc(100% - 80px)'
                  }}>
                    {titleValue[i]}
                  </div>
                </div>
              );
            })
          }
        </div>
        <div style={{ width: '100%', height: 'calc(100% - ' + (titleText.length * 25 + 5) + 'px)', overflow: 'scroll', backgroundColor: '#F5F5F9' }}>
          {
            formData.length > 0 && formData.map((item, i) => {
              return (
                <div className="yl-guoci" style={{ width: 'calc(100% - 22px)' }}>
                  <div className="yl-guoci-order"></div>
                  <div className="yl-guoci-orderText">{i + 1}</div>
                  <div style={{ display: 'flow-root' }}>
                    <div style={{ width: '100%', float: 'left', display: 'flex', marginTop: '6px' }}>
                      <div className="yl-cardTitle"> 操作人 </div>
                      <div className="yl-cardText"> {item['user']} </div>
                    </div>
                    <div style={{ width: '100%', float: 'left', display: 'flex', marginTop: '6px' }}>
                      <div className="yl-cardTitle"> 操作时间 </div>
                      <div className="yl-cardText"> {item['time']} </div>
                    </div>
                    {
                      formCol.map(key => {
                        if (item[valueResult] && key in JSON.parse(item[valueResult]) && JSON.parse(item[valueResult])[key] === '不合格') {
                          return (
                            <div style={{ width: '100%', float: 'left', display: 'flex', marginTop: '6px' }}>
                              <div className="yl-cardTitle"> {key.length > 10 ? key.slice(0, 10) + '...' : key} </div>
                              <div className="yl-cardText" style={{ color: 'red' }}> {item[key]} </div>
                            </div>
                          )
                        } else {
                          return (
                            <div style={{ width: '100%', float: 'left', display: 'flex', marginTop: '6px' }}>
                              <div className="yl-cardTitle"> {key.length > 10 ? key.slice(0, 10) + '...' : key}</div>
                              <div className="yl-cardText"> {item[key]} </div>
                            </div>
                          )
                        }
                      })
                    }
                  </div>
                  <div style={{ width: '100%', height: '36px', paddingTop: '6px', textAlign: 'left' }}>
                    <Button size="small" style={{ height: '30px', marginRight: '10px' }} onClick={() => showUpdateModal(item)}>
                      <FormOutlined />
                      修改
                    </Button>
                    <Button size="small" style={{ height: '30px', marginRight: '10px' }} onClick={() => copyAndUpdate(item)}>
                      <CopyOutlined />
                      复制
                    </Button>
                    {
                      item[valueResult] && Object.keys(item).some(key => JSON.parse(item[valueResult])[key] === '不合格') && (
                        <Button size="small" style={{ height: '30px', marginRight: '10px' }} onClick={() => putRight(item)}>
                          <EyeOutlined />
                          纠偏
                        </Button>

                      )
                    }
                    <Button size="small" style={{ height: '30px' }} onClick={() => {
                      Dialog.show({
                        content: '确认删除吗?',
                        closeOnAction: true,
                        actions: [
                          [
                            {
                              key: 'cancel',
                              text: '取消',
                            },
                            {
                              key: 'delete',
                              text: '确认',
                              danger: true,
                            },
                          ]
                        ],
                        onAction: (action, index) => {
                          if (action['key'] == 'delete') {
                            deleteColValue(item);
                          }
                        }
                      })
                    }}>
                      <DeleteOutlined />
                      删除
                    </Button>
                  </div>
                  {
                    item[valueResult] && item[valueWrong] && !Object.keys(item).some(key => JSON.parse(item[valueResult])[key] === '不合格') && (
                      <div className="yl-guoci-detail">
                        <ExclamationCircleOutlined style={{ fontSize: '16px' }} onClick={() => {
                          let errorData = [];
                          for (let key of Object.keys(JSON.parse(item[valueWrong]))) {
                            errorData.push({
                              key: key,
                              label: key,
                              children: JSON.parse(item[valueWrong])[key]
                            });
                          }
                          setBelowModal(errorData);
                          setMaskVisible(true);
                        }} />
                      </div>
                    )
                  }
                </div>
              );
            })
          }
        </div>
        <div className="yl-cardIconAdd" style={{ bottom: '88px', backgroundColor: '#2196F3', color: '#FFFFFF' }} onClick={() => showAddModal()}>
          <PlusOutlined className="yl-cardIcon" style={{ color: '#FFFFFF' }} />新建
        </div>
        <div className="yl-cardIconBack" style={{ bottom: '88px', backgroundColor: '#2196F3', color: '#FFFFFF' }} onClick={() => historyBack()}>
          <RollbackOutlined className="yl-cardIcon" style={{ color: '#FFFFFF' }} />返回
        </div>
        <Modal
          header={
            <div>
              <span style={{ marginRight: '15px' }}>{addOrUpdate}</span>
              <Button style={{ marginRight: '5px' }} color='primary' fill='outline' onClick={handleCancel}>取消</Button>
              {(addOrUpdate === '新建记录' || addOrUpdate === '复制记录') && (
                <Button style={{ marginRight: '5px' }} color='primary' fill='solid' onClick={addAndContinue}>确定并继续</Button>
              )}
              <Button color='primary' fill='solid' onClick={handleOk}>确定</Button>
            </div>
          }
          title={
            oneClickGet && <Button loading={oneClickLoading} key='get' onClick={handleGetOneClick}>一键获取</Button>
          }
          visible={open}
          bodyStyle={{ width: '350px', marginLeft: 'calc(50% - 175px)', paddingTop: '8px' }}
          className="yl_modalApp"
          content={
            <Form className="yl_formApp">
              {
                subTable.map((item, i) => {
                  if (filterData['获取方式'][item[valueType].substring(2, 38)] == '手动输入' && !isPutPight) {
                    return (
                      <Form.Item label={formCol[i]}>
                        <Input value={colValue[formCol[i]]} onChange={e => { changeColValue(formCol[i], e.target.value); }} />
                      </Form.Item>
                    );
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '换行输入' && !isPutPight) {
                    return (
                      <Form.Item label={formCol[i]}>
                        <TextArea placeholder={'换行输入'} rows={3} value={colValue[formCol[i]]}
                          style={{ marginTop: '3px' }}
                          onChange={e => { changeColValue(formCol[i], e); }}
                        />
                      </Form.Item>

                    );
                  }
                  else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '公式计算' && !isPutPight) {
                    return (
                      <Form.Item label={formCol[i]} extra={<a style={{ fontSize: '14px' }} onClick={() => getCount(formCol[i])}>计算</a>} >
                        <Input value={colValue[formCol[i]]} onChange={e => { changeColValue(formCol[i], e.target.value); }} />
                      </Form.Item>
                    );
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '设备获取') {
                    if (isPutPight && formCol[i] in below) {
                      return (
                        <Form.Item label={formCol[i]} extra={<a style={{ fontSize: '14px' }} onClick={() => getDataByTag(item, formCol[i], item[decimalPlaces])}>获取</a>}>
                          <Input
                            value={colValue[formCol[i]]}
                            onChange={e => { changeColValueByNumber(item, formCol[i], e.target.value); }}
                            onBlur={e => { reserveDecimal(item, formCol[i], e.target.value); }}
                            style={{ color: result[formCol[i]] === '不合格' ? 'red' : 'black' }}
                          />
                          <TextArea placeholder={formCol[i] + '字段纠偏措施'} rows={3} value={below[formCol[i]]}
                            style={{ marginTop: '3px' }}
                            onChange={(value) => {
                              let newdata = JSON.parse(JSON.stringify(below));
                              let newdatabak = JSON.parse(JSON.stringify(belowBak));
                              newdata[formCol[i]] = value;
                              newdatabak[formCol[i]] = value;
                              setBelow(newdata);
                              setBelowBak(newdatabak);
                            }} />
                        </Form.Item>
                      )
                    } else if (!isPutPight) {
                      return (
                        <Form.Item label={formCol[i]} extra={<a style={{ fontSize: '14px' }} onClick={() => getDataByTag(item, formCol[i], item[decimalPlaces])}>获取</a>}>
                          <Input
                            value={colValue[formCol[i]]}
                            onChange={e => { changeColValueByNumber(item, formCol[i], e.target.value); }}
                            onBlur={e => { reserveDecimal(item, formCol[i], e.target.value); }}
                            style={{ color: result[formCol[i]] === '不合格' ? 'red' : 'black' }}
                          />
                        </Form.Item>
                      );
                    }

                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == 'api获取' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                        extra={<a style={{ fontSize: '14px' }} onClick={() => getDataByApi(item[apiUrl], item[apiParam], formCol[i])}>获取</a>}
                      >
                        <Input value={colValue[formCol[i]]} onChange={e => { changeColValue(formCol[i], e.target.value); }} />
                      </Form.Item>
                    );
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '日期选择' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                        trigger='onConfirm'
                        onClick={async () => {
                          const value = await DatePicker.prompt({
                            value: colValue[formCol[i]] != '' ? dayjs(colValue[formCol[i]]).toDate() : null,
                            onConfirm: (date) => {
                              changeColValue(formCol[i], dayjs(date).format('YYYY-MM-DD'));
                            }
                          })
                        }}
                      >
                        <Input value={colValue[formCol[i]] ? dayjs(colValue[formCol[i]]).format('YYYY-MM-DD') : getDefaultTime('日期选择', item, formCol[i])} readOnly={true} />
                      </Form.Item>
                    );
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '时间选择' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                        trigger='onConfirm'
                        onClick={async () => {
                          const value = await Picker.prompt({
                            columns: timeColumns,
                            value: colValue[formCol[i]] != '' ? colValue[formCol[i]].split(':') : [dayjs().format('HH'), dayjs().format('mm')],
                            onConfirm: (value) => {
                              changeColValue(formCol[i], value.join(':'));
                            }
                          })
                        }}
                      >
                        <Input value={colValue[formCol[i]] ? colValue[formCol[i]] : getDefaultTime('时间选择', item, formCol[i])} readOnly={true} />
                      </Form.Item>
                    );
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '日期时间选择' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                        trigger='onConfirm'
                        onClick={async () => {
                          const value = await DatePicker.prompt({
                            value: colValue[formCol[i]] != '' ? dayjs(colValue[formCol[i]]).toDate() : null,
                            precision: 'minute',
                            onConfirm: (date) => {
                              changeColValue(formCol[i], dayjs(date).format('YYYY-MM-DD HH:mm'));
                            }
                          })
                        }}
                      >
                        <Input value={colValue[formCol[i]] ? colValue[formCol[i]] : getDefaultTime('日期时间选择', item, formCol[i])} readOnly={true} />
                      </Form.Item>
                    );
                  }
                  else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '时间段选择' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                      >
                        <Input style={{ width: '43%' }} value={parseTimeStringToValue('start', colValue[formCol[i]])} readOnly={true}
                          onClick={async () => {
                            const value = await Picker.prompt({
                              columns: timeColumns,
                              onConfirm: (value) => {
                                //获取当前字段的结束时间
                                let endTime = parseTimeStringToValue('end', colValue[formCol[i]]);
                                //新选的值的时间
                                let newStartTime = value.join(':');
                                let timeRange = null;
                                if (endTime) {
                                  const [endTimeHour, endTimeMin] = endTime.split(':')
                                  const [startTimeHour, startTimeMin] = newStartTime.split(':')
                                  if ((endTimeHour < startTimeHour) || (endTimeHour == startTimeHour && endTimeMin < startTimeMin)) {
                                    const temp = newStartTime
                                    newStartTime = endTime
                                    endTime = temp
                                  }
                                  timeRange = newStartTime + ' - ' + endTime;
                                } else {
                                  timeRange = newStartTime + ' - ' + '';
                                }
                                changeColValue(formCol[i], timeRange);
                              }
                            })
                          }}
                        />
                        <SwapRightOutlined />
                        <Input style={{ width: '43%' }} value={parseTimeStringToValue('end', colValue[formCol[i]])} readOnly={true}
                          onClick={async () => {
                            const value = await Picker.prompt({
                              columns: timeColumns,
                              onConfirm: (value) => {
                                let startTime = parseTimeStringToValue('start', colValue[formCol[i]]);
                                let newEndTime = value.join(':');
                                let timeRange = null;
                                if (startTime) {
                                  const [startTimeHour, startTimeMin] = startTime.split(':')
                                  const [endTimeHour, endTimeMin] = newEndTime.split(':')
                                  if ((endTimeHour < startTimeHour) || (endTimeHour == startTimeHour && endTimeMin < startTimeMin)) {
                                    const temp = newEndTime
                                    newEndTime = startTime
                                    startTime = temp
                                  }
                                  timeRange = startTime + ' - ' + newEndTime;
                                } else {
                                  timeRange = '' + ' - ' + newEndTime;
                                }
                                changeColValue(formCol[i], timeRange);
                              }
                            })
                          }}
                        />
                      </Form.Item>
                    );
                  }
                  else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '日期时间段选择' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                      >
                        <Input style={{ width: '43%' }} value={parseTimeStringToValue('start', colValue[formCol[i]])} readOnly={true}
                          onClick={async () => {
                            const value = await DatePicker.prompt({
                              precision: 'minute',
                              onConfirm: (value) => {
                                //获取当前字段的结束时间
                                let endTimeStr = parseTimeStringToValue('end', colValue[formCol[i]]);
                                //新选的值的时间
                                let newStartTimeStr = dayjs(value).format('YYYY-MM-DD HH:mm')
                                let timeRange = null;
                                if (endTimeStr) {
                                  const endTime = dayjs(endTime);
                                  if (endTime < value) {
                                    const temp = newStartTimeStr
                                    newStartTimeStr = endTimeStr
                                    endTimeStr = temp
                                  }
                                  timeRange = newStartTimeStr + ' - ' + endTimeStr;
                                } else {
                                  timeRange = newStartTimeStr + ' - ' + '';
                                }
                                changeColValue(formCol[i], timeRange);
                              }
                            })
                          }}
                        />
                        <SwapRightOutlined />
                        <Input style={{ width: '43%' }} value={parseTimeStringToValue('end', colValue[formCol[i]])} readOnly={true}
                          onClick={async () => {
                            const value = await DatePicker.prompt({
                              precision: 'minute',
                              onConfirm: (value) => {
                                //获取当前字段的结束时间
                                let startTimeStr = parseTimeStringToValue('start', colValue[formCol[i]]);
                                //新选的值的时间
                                let newEndTimeStr = dayjs(value).format('YYYY-MM-DD HH:mm')
                                let timeRange = null;
                                if (startTimeStr) {
                                  const startTime = dayjs(startTimeStr);
                                  if (value < startTime) {
                                    const temp = newEndTimeStr
                                    newEndTimeStr = startTimeStr
                                    startTimeStr = temp
                                  }
                                  timeRange = startTimeStr + ' - ' + newEndTimeStr;
                                } else {
                                  timeRange = '' + ' - ' + newEndTimeStr;
                                }
                                changeColValue(formCol[i], timeRange);
                              }
                            })
                          }}
                        />
                      </Form.Item>
                    );
                  }
                  else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '下拉选择' && !isPutPight) {
                    return (
                      <Form.Item
                        label={formCol[i]}
                        trigger='onConfirm'
                        onClick={async () => {
                          const value = await Cascader.prompt({
                            value: [colValue[formCol[i]]],
                            options: getSelectOption(filterData['下拉文本信息'][item[selectValue].substring(2, 38)]),
                            onConfirm: (value) => {
                              changeColValue(formCol[i], value[0]);
                            },
                          });
                        }}
                      >
                        <Input value={colValue[formCol[i]]} readOnly={true} />
                      </Form.Item>
                    );
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '数字输入') {
                    if (isPutPight && formCol[i] in below) {
                      return (
                        <Form.Item label={formCol[i]}>
                          <Input
                            value={colValue[formCol[i]]}
                            onChange={e => { changeColValueByNumber(item, formCol[i], e.target.value); }}
                            onBlur={e => { reserveDecimal(item, formCol[i], e.target.value); }}
                            type="number"
                            style={{ color: result[formCol[i]] === '不合格' ? 'red' : 'black' }}
                          />
                          <TextArea placeholder={formCol[i] + '字段纠偏措施'} rows={3} value={below[formCol[i]]}
                            style={{ marginTop: '3px' }}
                            onChange={(value) => {
                              let newdata = JSON.parse(JSON.stringify(below));
                              let newdatabak = JSON.parse(JSON.stringify(belowBak));
                              newdata[formCol[i]] = value;
                              newdatabak[formCol[i]] = value;
                              setBelow(newdata);
                              setBelowBak(newdatabak);
                            }} />
                        </Form.Item>
                      )
                    } else if (!isPutPight) {
                      return (
                        <Form.Item label={formCol[i]}>
                          <Input
                            value={colValue[formCol[i]]}
                            onChange={e => { changeColValueByNumber(item, formCol[i], e.target.value); }}
                            onBlur={e => { reserveDecimal(item, formCol[i], e.target.value); }}
                            type="number"
                            style={{ color: result[formCol[i]] === '不合格' ? 'red' : 'black' }}
                          />
                        </Form.Item>
                      );
                    }
                  } else if (filterData['获取方式'][item[valueType].substring(2, 38)] == '复选框选择') {
                    if (filterData['复选框类型'][item[checkType].substring(2, 38)] == '多选' && !isPutPight) {
                      return (
                        <Form.Item label={formCol[i]}>
                          <Selector
                            columns={2}
                            multiple
                            value={colValue[formCol[i]] != '' ? colValue[formCol[i]].split('，') : []}
                            options={getSelectOption(filterData['复选框文本信息'][item[checkValue].substring(2, 38)])}
                            onChange={(value, extend) => {
                              changeCheckValue(formCol[i], value, false);
                            }}
                          />
                        </Form.Item>
                      );
                    } else if (filterData['复选框类型'][item[checkType].substring(2, 38)] == '单选') {
                      if (isPutPight && formCol[i] in below) {
                        return (
                          <Form.Item label={formCol[i]}>
                            <Selector
                              columns={2}
                              value={colValue[formCol[i]] != '' ? colValue[formCol[i]].split('，')[0] : []}
                              options={getSelectOption(filterData['复选框文本信息'][item[checkValue].substring(2, 38)])}
                              onChange={(value, extend) => {
                                changeCheckValue(formCol[i], value, true);
                              }}
                            />
                            <TextArea placeholder={formCol[i] + '字段纠偏措施'} rows={3} value={below[formCol[i]]}
                              style={{ marginTop: '3px' }}
                              onChange={(value) => {
                                let newdata = JSON.parse(JSON.stringify(below));
                                let newdatabak = JSON.parse(JSON.stringify(belowBak));
                                newdata[formCol[i]] = value;
                                newdatabak[formCol[i]] = value;
                                setBelow(newdata);
                                setBelowBak(newdatabak);
                              }} />
                          </Form.Item>
                        );
                      } else if (!isPutPight) {
                        return (
                          <Form.Item label={formCol[i]}>
                            <Selector
                              columns={2}
                              value={colValue[formCol[i]] != '' ? colValue[formCol[i]].split('，')[0] : []}
                              options={getSelectOption(filterData['复选框文本信息'][item[checkValue].substring(2, 38)])}
                              onChange={(value, extend) => {
                                changeCheckValue(formCol[i], value, true);
                              }}
                            />
                          </Form.Item>
                        );
                      }
                    }
                  }
                })
              }
            </Form>
          }
        />
        <Popup visible={maskVisible} onMaskClick={() => { setMaskVisible(false) }} onClose={() => { setMaskVisible(false) }} >
          <div style={{ height: '40vh', overflowY: 'scroll', padding: '20px' }}>
            <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '7px' }}>纠偏措施</div>
            {
              belowModal.map((item) => {
                return (
                  <div style={{ width: '100%', marginBottom: '7px', display: 'flex' }}>
                    <div className="yl-cardTitle">{item['label'].length > 10 ? item['label'].slice(0, 10) + '...' : item['label']} </div>
                    <div className="yl-cardText"> {item['children']} </div>
                  </div>
                );
              })
            }
          </div>
        </Popup>
      </div>
    </ConfigProvider>
  );
}
