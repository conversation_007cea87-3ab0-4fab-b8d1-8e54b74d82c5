import React, { useEffect, useState, useLayoutEffect, useRef, useCallback } from "react";
import { env, config, api, utils, md_emitter } from "mdye";
import { ConfigProvider, Button, Table, Modal, Input, Select, DatePicker, Popconfirm, message, Space, TimePicker, InputNumber, Checkbox, Radio, Descriptions } from 'antd';
import { PlusOutlined, RollbackOutlined, DeleteOutlined, EyeOutlined, FormOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import zhCN from 'antd/es/locale/zh_CN';
import dayjs from 'dayjs';
import axios from 'axios';
import "./style.less";
import moment from 'moment';
import 'moment/locale/zh-cn';
moment.locale('zh-cn')

const { getFilterRows, getRowRelationRows, addWorksheetRow, updateWorksheetRow, deleteWorksheetRow } = api;
const { openRecordInfo } = utils;

export default function () {
  const { appId, worksheetId, viewId, controls, query } = config;
  const [filters, setFilters] = useState(config.filters || {});
  const [messageApi, contextHolder] = message.useMessage();

  const [filterData, setFilterData] = useState({});
  const [titleText, setTitleText] = useState([]);
  const [titleValue, setTitleValue] = useState([]);
  const [formCol, setFormCol] = useState([]);
  const [formData, setFormData] = useState([]);
  const [subTable, setSubTable] = useState([]);
  const [colValue, setColValue] = useState({});
  const [open, setOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [addOrUpdate, setAddOrUpdate] = useState('新建记录');
  const [updateRowId, setUpdateRowId] = useState('');
  //判定结果值
  const [result, setResult] = useState({});
  //纠偏措施(输入框)
  const [below, setBelow] = useState({});
  const [belowBak, setBelowBak] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [belowModal, setBelowModal] = useState([]);
  //是否纠偏
  const [isPutPight, setIsPutPight] = useState(false);

  //表头字段
  const [headerField, setHeaderField] = useState('');
  //获取方式字段
  const [valueType, setValueType] = useState('');
  //下拉文本信息字段
  const [selectValue, setSelectValue] = useState('');
  //复选框文本信息字段
  const [checkValue, setCheckValue] = useState('');
  //复选框类型字段
  const [checkType, setCheckType] = useState('');
  //默认时间字段
  const [defaultTime, setDefaultTime] = useState('');
  //设备点位字段
  const [tag, setTag] = useState('');
  //获取api数据
  const [apiUrl, setApiUrl] = useState('');
  //api参数
  const [apiParam, setApiParam] = useState('');
  //上限
  const [topLimit, setTopLimit] = useState('');
  //下限
  const [lowerLimit, setLowerLimit] = useState('');
  //小数位数
  const [decimalPlaces, setDecimalPlaces] = useState(null);
  //执行表ID
  const [valueTable, setValueTable] = useState('');
  //配置主表关联字段
  const [valueLink, setValueLink] = useState('');
  //执行表操作人字段
  const [valueUser, setValueUser] = useState('');
  //执行表创建时间字段
  const [valueCTime, setValueCTime] = useState('');
  //执行表修改时间字段
  const [valueMTime, setValueMTime] = useState('');
  //执行表明细字段
  const [valueField, setValueField] = useState('');
  //执行表判定结果字段
  const [valueResult, setValueResult] = useState('');
  //执行表纠偏措施字段
  const [valueWrong, setValueWrong] = useState('');
  //逻辑删除字段
  const [deleteFlag, setDeleteFlag] = useState('');

  //计算公式参数-表单名称
  const [formName, setFormName] = useState('');
  //计算公式参数-任务ID
  const [taskId, setTaskId] = useState('');
  //一键获取设备数据按钮
  const [oneClickGet, setOneClickGet] = useState(false);
  const [oneClickLoading, setOneClickLoading] = useState(false);

  //查询【执行】生产表
  async function getColValue (dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, valueResult, dataWrong) {
    const res = await getFilterRows({
      worksheetId: dataSourse,
      pageIndex: 1,
      pageSize: 99999999,
      sortId: 'ctime',
      fastFilters: [{
        controlId: dataLink,
        dataType: 29,
        dateRange: 0,
        filterType: 24,
        maxValue: "",
        minValue: "",
        spliceType: 1,
        value: '',
        values: [query['id']]
      }, {
        controlId: dataFlag,
        dataType: 6,
        dateRange: 0,
        filterType: 2,
        maxValue: "",
        minValue: "",
        spliceType: 1,
        value: '',
        values: ['1']
      }]
    });
    let newColData = [];
    for (let i = 0; i < res.data.length; i++) {
      newColData[i] = JSON.parse(res.data[i][dataValue]);
      newColData[i]['rowid'] = res.data[i].rowid;
      newColData[i]['user'] = res.data[i][dataUser];
      newColData[i]['time'] = res.data[i][dataMTime];
      newColData[i]['id'] = i + 1;
      newColData[i][valueResult] = res.data[i][valueResult];
      newColData[i][dataWrong] = res.data[i][dataWrong];

    }
    setFormData(newColData);
  }

  //新增【执行】生产表
  async function addColValue () {
    let receiveControl = [{
      controlId: valueField,
      value: JSON.stringify(colValue)
    }, {
      controlId: valueLink,
      value: JSON.stringify([{
        sid: query['id']
      }])
    }, {
      controlId: valueUser,
      value: config.currentAccount.fullname
    }, {
      controlId: valueCTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }, {
      controlId: valueMTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }, {
      controlId: deleteFlag,
      value: 1
    }, {
      controlId: valueResult,
      value: JSON.stringify(result) != '{}' ? JSON.stringify(result) : ''
    }, {
      controlId: valueWrong,
      value: JSON.stringify(below) != '{}' ? JSON.stringify(below) : ''
    }];
    //如果纠偏不合格不让保存
    if (isPutPight) {
      const isError = Object.keys(result).some(key => result[key] === '不合格')
      if (isError) {
        messageApi.open({
          type: 'error',
          content: '纠偏依然存在不合格',
        });
        return true;
      }
    }
    const res = await addWorksheetRow({
      worksheetId: valueTable,
      appId,
      receiveControls: receiveControl
    });
    getColValue(valueTable, valueLink, valueField, deleteFlag, valueUser, valueMTime, valueResult, valueWrong);
  }

  //修改【执行】生产表
  async function updateColValue () {
    let receiveControl = [{
      controlId: valueField,
      value: JSON.stringify(colValue)
    }, {
      controlId: valueMTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }, {
      controlId: valueResult,
      value: JSON.stringify(result) != '{}' ? JSON.stringify(result) : ''
    }, {
      controlId: valueWrong,
      value: JSON.stringify(below) != '{}' ? JSON.stringify(below) : ''
    }];
    const res2 = await updateWorksheetRow({
      appId,
      worksheetId: valueTable,
      rowId: updateRowId,
      newOldControl: receiveControl
    });
    getColValue(valueTable, valueLink, valueField, deleteFlag, valueUser, valueMTime, valueResult, valueWrong);
  }

  //删除【执行】生产表
  async function deleteColValue (record, dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, dataResult, dataWrong) {
    let receiveControl = [{
      controlId: dataFlag,
      value: 0
    }, {
      controlId: dataMTime,
      value: dayjs().format('YYYY-MM-DD HH:mm')
    }];
    const res2 = await updateWorksheetRow({
      appId,
      worksheetId: dataSourse,
      rowId: record['rowid'],
      newOldControl: receiveControl
    });
    getColValue(dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, dataResult, dataWrong);
  }

  //最开始调用的方法
  async function loadRecords () {
    //获取选项集
    axios.post(env.url, {
      appKey: env.appKey,
      sign: env.sign
    })
      .then(function (response) {
        let s = {};
        for (let i = 0; i < response.data.data.length; i++) {
          s[response.data.data[i].name] = {};
          for (let j = 0; j < response.data.data[i].options.length; j++) {
            s[response.data.data[i].name][response.data.data[i].options[j].key] = response.data.data[i].options[j].value;
          }
        }        
        setFilterData(s);
      })
      .catch(function (error) {
        console.log(error);
      })

    //查询配置主表
    const res1 = await getFilterRows({
      worksheetId,
      viewId,
      pageIndex: 1,
      pageSize: 99999999,
      ...filters,
      fastFilters: [{
        controlId: 'rowid',
        dataType: 2,
        dateRange: 0,
        filterType: 1,
        maxValue: "",
        minValue: "",
        spliceType: 1,
        value: '',
        values: [query['id']]
      }]
    });
    //主表要显示的字段(表头)
    const mainCols = env.visibleFields.split("，");
    setTitleText(mainCols);
    //所有的配置主表字段
    const fields = config.controls;
    //通过名称找到要显示的字段的值(表头)
    let mainList = [];
    mainCols.forEach(element => {
      for (let i = 0; i < fields.length; i++) {
        if (fields[i].controlName == element) {
          mainList.push(res1.data[0][fields[i].controlId]);
          break;
        }
      }
    });
    //表头值
    setTitleValue(mainList);
    //计算公司参数-表单名称
    setFormName(res1.data[0][env.formName[0]]);
    //计算公司参数-任务ID
    setTaskId(res1.data[0][env.taskId[0]]);
    //subTable获取从表字段,配置从表字段中文名 转为 id
    let headField = '';
    for (let i = 0; i < fields.length; i++) {
      //拿到从表
      if (fields[i].controlId == env.subTable[0]) {
        //遍历从表
        for (let j = 0; j < fields[i].relationControls.length; j++) {
          if (fields[i].relationControls[j].controlName == env.headerField) {
            setHeaderField(fields[i].relationControls[j].controlId);
            headField = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueType) {            
            setValueType(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.selectValue) {
            setSelectValue(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.tag) {
            setTag(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.apiUrl) {
            setApiUrl(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.apiParam) {
            setApiParam(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.defaultTime) {
            setDefaultTime(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.topLimit) {
            setTopLimit(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.lowerLimit) {
            setLowerLimit(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.checkValue) {
            setCheckValue(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.checkType) {
            setCheckType(fields[i].relationControls[j].controlId);
          } else if (fields[i].relationControls[j].controlName == env.decimalPlaces) {
            setDecimalPlaces(fields[i].relationControls[j].controlId);
          }
        }
        break;
      }
    }

    //【执行】生产表字段中文名 转为 id
    let dataSourse = '';
    let dataValue = '';
    let dataFlag = '';
    let dataLink = '';
    let dataUser = '';
    let dataCtime = '';
    let dataMTime = '';
    let dataWrong = '';
    let dataResult = '';
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].controlId == env.valueTable[0]) {
        setValueTable(fields[i].dataSource);
        dataSourse = fields[i].dataSource;
        for (let j = 0; j < fields[i].relationControls.length; j++) {
          if (fields[i].relationControls[j].controlName == env.valueLink) {
            setValueLink(fields[i].relationControls[j].controlId);
            dataLink = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueField) {
            setValueField(fields[i].relationControls[j].controlId);
            dataValue = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.deleteFlag) {
            setDeleteFlag(fields[i].relationControls[j].controlId);
            dataFlag = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueUser) {
            setValueUser(fields[i].relationControls[j].controlId);
            dataUser = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueCTime) {
            setValueCTime(fields[i].relationControls[j].controlId);
            dataCtime = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueMTime) {
            setValueMTime(fields[i].relationControls[j].controlId);
            dataMTime = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueWrong) {
            setValueWrong(fields[i].relationControls[j].controlId);
            dataWrong = fields[i].relationControls[j].controlId;
          } else if (fields[i].relationControls[j].controlName == env.valueResult) {
            setValueResult(fields[i].relationControls[j].controlId);
            dataResult = fields[i].relationControls[j].controlId;
          }
        }
        break;
      }
    }
    //查询配置从表
    const res = await getRowRelationRows({
      controlId: env.subTable[0],
      rowId: query['id'],
      worksheetId,
      pageIndex: 1,
      pageSize: 99999999,
    });
    //配置从表数据
    setSubTable(res.data);
    let newColumns = [{
      title: '序号',
      dataIndex: 'id',
      fixed: 'left',
      key: 'id',
      width: 50,
    }];
    let defaultData = {};
    for (let i = 0; i < res.data.length; i++) {
      newColumns.push({
        title: res.data[i][headField],
        dataIndex: res.data[i][headField],
        key: res.data[i][headField],
        width: 160,
        ellipsis: true,
        render: (text, record, index) => {
          if (record[dataResult] && res.data[i][headField] in JSON.parse(record[dataResult]) && JSON.parse(record[dataResult])[res.data[i][headField]] == '不合格') {
            return (
              <span style={{ color: 'red' }}>{text}</span>
            )
          } else {
            return (
              text
            )
          }
        }
      });
      defaultData[res.data[i][headField]] = '';
    }
    newColumns.push({
      title: 'rowid',
      dataIndex: 'rowid',
      hidden: true,
      key: 'rowid',
      width: 0,
    });
    newColumns.push({
      title: dataWrong,
      dataIndex: dataWrong,
      hidden: true,
      key: dataWrong,
      width: 0,
    });
    newColumns.push({
      title: dataResult,
      dataIndex: dataResult,
      hidden: true,
      key: dataResult,
      width: 0,
    });
    newColumns.push({
      title: '操作人',
      dataIndex: 'user',
      fixed: 'right',
      key: 'user',
      width: 80,
    });
    newColumns.push({
      title: '操作时间',
      dataIndex: 'time',
      fixed: 'right',
      key: 'time',
      width: 130,
    });
    newColumns.push({
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 250,
      render: (_, record) =>
        formData.length >= 0 ? (
          <Space size="small" style={{ width: '250px' }}>
            <Button size="small" type="text" style={{ color: '#1677FF' }} onClick={() => showUpdateModal(record, dataResult, dataWrong)}>修改</Button>
            <Button size="small" type="text" style={{ color: '#1677FF' }} onClick={() => copyAndUpdate(record, dataResult, dataWrong)}>复制</Button>
            <Popconfirm title="确认删除吗?" okText="确认" cancelText="取消"
              onConfirm={() => deleteColValue(record, dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, dataResult, dataWrong)}>
              <Button size="small" type="text" style={{ color: 'red' }}>删除</Button>
            </Popconfirm>
            {
              record[dataResult] && Object.keys(record).some(key => JSON.parse(record[dataResult])[key] === '不合格') && (
                <div>
                  <Button size="small" type="text" style={{ color: '#1677FF' }} onClick={() => putRight(record, dataResult, dataWrong)}>纠偏</Button>
                </div>
              )
            }
            {
              record[dataResult] && record[dataWrong] && !Object.keys(record).some(key => JSON.parse(record[dataResult])[key] === '不合格') && (
                <div>
                  <Button size="small" type="text" icon={<ExclamationCircleOutlined />}
                    onClick={() => {
                      let errorData = [];
                      for (let key of Object.keys(JSON.parse(record[dataWrong]))) {
                        errorData.push({
                          key: key,
                          label: key,
                          children: JSON.parse(record[dataWrong])[key]
                        });
                      }
                      setBelowModal(errorData);
                      setIsModalOpen(true);
                    }} />
                </div>
              )
            }
          </Space>
        ) : null
    });
    setFormCol(newColumns);
    setColValue(defaultData);
    getColValue(dataSourse, dataLink, dataValue, dataFlag, dataUser, dataMTime, dataResult, dataWrong);
  }

  //最开始执行一次
  useEffect(() => {
    loadRecords();
  }, []);
  //更新一键获取的状态
  useEffect(() => {
    subTable.map((item, i) => {
      if(filterData['获取方式'][item[valueType].substring(2, 38)] == '设备获取'){    
        setOneClickGet(true)
      }
    })
  }, [subTable]); 

  //新建记录按钮执行
  const showAddModal = () => {
    for (let key of Object.keys(colValue)) {
      colValue[key] = '';
    }
    let newData = JSON.parse(JSON.stringify(colValue));
    setColValue(newData);
    setBelow({});
    setBelowBak({});
    setAddOrUpdate('新建记录');
    setOpen(true);
  };

  //修改按钮执行
  const showUpdateModal = (record, dataResult, dataWrong) => {
    let newData = {};
    for (let key of Object.keys(record)) {
      if (key != 'rowid' || key != dataWrong) {
        newData[key] = record[key]
      }
    }
    if (record[dataResult]) {
      setResult(JSON.parse(record[dataResult]))
    }
    if (record[dataWrong]) {
      setBelow(JSON.parse(record[dataWrong]));
      setBelowBak(JSON.parse(record[dataWrong]));
    } else {
      setBelow({});
      setBelowBak({});
    }
    setColValue(newData);
    setUpdateRowId(record.rowid);
    setAddOrUpdate('修改记录');
    setOpen(true);
  };

  //复制并修改按钮执行
  const copyAndUpdate = (record, dataResult, dataWrong) => {
    let newData = {};
    for (let key of Object.keys(record)) {
      if (key != 'rowid' || key != dataWrong) {
        newData[key] = record[key]
      }
    }
    if (record[dataResult]) {
      setResult(JSON.parse(record[dataResult]))
    }
    if (record[dataWrong]) {
      setBelow(JSON.parse(record[dataWrong]));
      setBelowBak(JSON.parse(record[dataWrong]));
    } else {
      setBelow({});
      setBelowBak({});
    }
    setColValue(newData);
    //setUpdateRowId(record.rowid);
    setAddOrUpdate('复制记录');
    setOpen(true);
  };
  //纠偏措施
  const putRight = (record, dataResult, dataWrong) => {
    setIsPutPight(true)
    let newData = {};
    for (let key of Object.keys(record)) {
      if ((key != 'rowid' || key != dataWrong)) {
        newData[key] = record[key]
      }
    }
    if (record[dataResult]) {
      setResult(JSON.parse(record[dataResult]))
    }
    if (record[dataWrong]) {
      setBelow(JSON.parse(record[dataWrong]));
      setBelowBak(JSON.parse(record[dataWrong]));
    } else {
      setBelow({});
      setBelowBak({});
    }
    setColValue(newData);
    //setUpdateRowId(record.rowid);
    setAddOrUpdate('纠偏措施');
    setOpen(true);
  }

  //新增弹窗确认
  const handleOk = () => {
    setConfirmLoading(true);
    if (addOrUpdate === '新建记录' || addOrUpdate === '复制记录' || addOrUpdate === '纠偏措施') {
      const myPromise = addColValue();
      myPromise.then((value) => {
        if (value != true) {
          setConfirmLoading(false);
          setIsPutPight(false)
          setOpen(false);
        }
      })
    } else {
      updateColValue();
      setConfirmLoading(false);
      setIsPutPight(false)
      setOpen(false);
    }
  };
  //新增并继续
  const addAndContinue = () => {
    setConfirmLoading(true);
    addColValue();
    setConfirmLoading(false);
    messageApi.open({
      type: 'success',
      content: '提交成功',
    });
  };
  //新增弹窗取消
  const handleCancel = () => {
    setIsPutPight(false)
    setOpen(false);
  };
  /**
   * 一键获取方法
   */
  const handleGetOneClick = async () => { 
    setOneClickLoading(true) 
    const promises = subTable.map((item,i) => {  
      if (filterData['获取方式'][item[valueType].substring(2, 38)] === '设备获取') {  
        const title = formCol[i+1].title; // 请确保这里获取的 title 是正确的  
        const decimalPlace = item[decimalPlaces];  
        return getOneDataByTag(item, title, decimalPlace);  
      }  
      return Promise.resolve(null); // 如果不满足条件，返回 null  
    });
    const results = await Promise.all(promises);  
    const newData = { ...colValue };  
    const newBelow = { ...below };  
    const newResult = { ...result }; // 现有结果状态的拷贝  
    results.forEach(result => {  
      if (result) {  
        const { name, tempValue, judge } = result;  
  
        // 更新新的数据  
        newData[name] = tempValue;  
  
        // 更新结果状态  
        newResult[name] = judge ? '不合格' : '合格';  
  
        // 更新 below 状态  
        if (judge) {  
          newBelow[name] = belowBak[name] || '';  
        } else {  
          delete newBelow[name];  
        }  
      }  
    });  
    // 一次性更新状态  
    setColValue(newData);  
    setResult(newResult);  
    setBelow(newBelow);  
    console.log('所有设备数据已成功获取和更新！');  
    setOneClickLoading(false) 
  };  

  //获取下拉框选项
  const getSelectOption = (options) => {
    let result = [];
    const list = options.split("；");
    list.map(item => {
      result.push({
        value: item,
        label: item
      });
    })
    return result;
  };

  //获取多选复选框选项
  function getMulCheckOption (options) {
    const list = options.split("；");
    return (<div style={{ height: '100%', width: '100%', display: 'flex' }}>
      {
        list.map((item) => {
          return (
            <div style={{ height: '30px', width: (100 / list.length) + '%' }}>
              <div style={{ height: '100%', maxWidth: '90%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                <Checkbox value={item}>{item}</Checkbox>
              </div>
            </div>
          )
        })
      }
    </div>);
  }
  //获取单选复选框选项
  function getSinCheckOption (options) {
    const list = options.split("；");
    return (<div style={{ height: '100%', width: '100%', display: 'flex' }}>
      {
        list.map((item) => {
          return (
            <div style={{ height: '30px', width: (100 / list.length) + '%' }}>
              <div style={{ height: '100%', maxWidth: '90%', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                <Checkbox value={item}>{item}</Checkbox>
              </div>
            </div>
          )
        })
      }
    </div>);
  }
  //一键获取设备数据
  const getOneDataByTag = async (item, name, decimalPlaces) => {  
    const max = item[topLimit];  
    const min = item[lowerLimit];  
    try {  
      const response = await axios.get(env.tagUrl, { params: { tag: item[tag] } });  
      let tempValue = response.data.value;  
      const judge = (min && parseFloat(tempValue) < parseFloat(min)) || (max && parseFloat(tempValue) > parseFloat(max));
      // 处理小数位  
      if (decimalPlaces && !isNaN(tempValue)) {  
        const count = (tempValue.toString().split('.')[1] || []).length;  
        if (count > decimalPlaces) {  
          tempValue = parseFloat(tempValue).toFixed(decimalPlaces);  
        }  
      }  
      return { name, tempValue, judge }; // 返回处理后的结果  
    } catch (error) {  
      console.error('获取数据时发生错误:', error);  
      return null; // 返回 null 表示发生了错误  
    }  
  };  

  //获取设备数据
  const getDataByTag = (item, name, decimalPlaces) => {
    const max = item[topLimit];
    const min = item[lowerLimit];
    axios.get(env.tagUrl, {
      params: {
        tag: item[tag]
      }
    })
      .then(function (response) {
        let newData = JSON.parse(JSON.stringify(colValue));
        let tempValue = response.data.value;
        const judge = (min && parseFloat(tempValue) < parseFloat(min)) || (max && parseFloat(tempValue) > parseFloat(max))
        //新增判定结果值
        let parseResult = JSON.parse(JSON.stringify(result));
        judge ? parseResult[name] = '不合格' : parseResult[name] = '合格'
        setResult(parseResult)
        //保留小数位数
        if (decimalPlaces) {
          if (!isNaN(Number(tempValue))) {
            const count = tempValue.toString().split('.')[1].length
            let parseValue = parseFloat(tempValue)
            if (parseValue % 1 != 0 && count > decimalPlaces) {
              parseValue = parseValue.toFixed(decimalPlaces)
              tempValue = parseValue
            }
          }
        }
        if (judge) {
          if (!(name in below)) {
            let newdata = JSON.parse(JSON.stringify(below));
            let newdatabak = JSON.parse(JSON.stringify(belowBak));
            newdata[name] = newdatabak[name] ? newdatabak[name] : '';
            setBelow(newdata);
          }
        } else {
          let newdata = JSON.parse(JSON.stringify(below));
          delete newdata[name];
          setBelow(newdata);
        }
        newData[name] = tempValue
        setColValue(newData);
      })
      .catch(function (error) {
        console.log(error);
      })
  } 

  //api获取数据
  const getDataByApi = (url, param, name) => {
    axios.get(url, {
      params: JSON.parse(param)
    })
      .then(function (response) {
        let newData = JSON.parse(JSON.stringify(colValue));
        newData[name] = response.data.value;
        setColValue(newData);
      })
      .catch(function (error) {
        console.log(error);
      })
  }

  //公式计算
  const getCount = (name) => {
    axios.get(env.formula, {
      params: {
        x: colValue[name],
        fieldName: name,
        formName: formName,
        taskId: taskId
      }
    })
      .then(function (response) {
        let newData = JSON.parse(JSON.stringify(colValue));
        newData[name] = response.data.y;
        setColValue(newData);
      })
      .catch(function (error) {
        console.log(error);
      })
  }

  //弹窗输入
  const changeColValue = (name, value) => {
    let newData = JSON.parse(JSON.stringify(colValue));
    newData[name] = value;
    setColValue(newData);
  }

  //设备/数字输入
  const changeColValueByNumber = (item, name, value) => {
    const max = item[topLimit];
    const min = item[lowerLimit];
    const judge = (min && parseFloat(value) < parseFloat(min)) || (max && parseFloat(value) > parseFloat(max))
    //新增判定结果值
    let parseResult = JSON.parse(JSON.stringify(result));
    judge ? parseResult[name] = '不合格' : parseResult[name] = '合格'
    setResult(parseResult)
    if (!isPutPight) {
      if (judge) {
        if (!(name in below)) {
          let newdata = JSON.parse(JSON.stringify(below));
          let newdatabak = JSON.parse(JSON.stringify(belowBak));
          newdata[name] = newdatabak[name] ? newdatabak[name] : '';
          setBelow(newdata);
        }
      } else {
        let newdata = JSON.parse(JSON.stringify(below));
        delete newdata[name];
        setBelow(newdata);
      }
    } else {
      if (!(name in below)) {
        let newdata = JSON.parse(JSON.stringify(below));
        let newdatabak = JSON.parse(JSON.stringify(belowBak));
        newdata[name] = newdatabak[name] ? newdatabak[name] : '';
        setBelow(newdata);
      }
    }
    let newData = JSON.parse(JSON.stringify(colValue));
    newData[name] = value;
    setColValue(newData);
  }
  //数字输入，设备获取输入保留小数位数
  const reserveDecimal = (item, name, value) => {
    if (value) {
      const digit = item[decimalPlaces];
      const count = value.toString().split('.')[1].length
      if (!isNaN(Number(value)) && digit) {
        let parseValue = parseFloat(value)
        if (parseValue % 1 != 0 && count > digit) {
          parseValue = parseValue.toFixed(digit)
          let newData = JSON.parse(JSON.stringify(colValue));
          newData[name] = parseValue;
          setColValue(newData);
        }
      }
    }
  }

  //复选框
  const changeCheckValue = (name, value, type) => {
    if (type == '多选') {
      let newData = JSON.parse(JSON.stringify(colValue));
      newData[name] = value.join("，");
      setColValue(newData);
    } else {
      //点击第二个元素时删除第一个元素，实现单选
      if (value.length == 2) {
        const tempArr = []
        tempArr.push(value[1])
        value = tempArr
      }
      let newData = JSON.parse(JSON.stringify(colValue));
      newData[name] = value.join("，");
      setColValue(newData);
      //判定结果值
      let parseResult = JSON.parse(JSON.stringify(result));
      if (value == 'NG') {
        parseResult[name] = '不合格'
        setResult(parseResult)
      } else if (value == 'OK') {
        parseResult[name] = '合格'
        setResult(parseResult)
      }
      else {
        delete parseResult[name];
      }
      //是否是纠偏
      if (!isPutPight) {
        if (value == 'NG') {
          if (!(name in below)) {
            let newdata = JSON.parse(JSON.stringify(below));
            let newdatabak = JSON.parse(JSON.stringify(belowBak));
            newdata[name] = newdatabak[name] ? newdatabak[name] : '';
            setBelow(newdata);
          }
        } else {
          let newdata = JSON.parse(JSON.stringify(below));
          delete newdata[name];
          setBelow(newdata);
        }
      } else {
        if (!(name in below)) {
          let newdata = JSON.parse(JSON.stringify(below));
          let newdatabak = JSON.parse(JSON.stringify(belowBak));
          newdata[name] = newdatabak[name] ? newdatabak[name] : '';
          setBelow(newdata);
        }
      }
    }
  }

  //默认时间
  const getDefaultTime = (type, item, name) => {
    let newData = JSON.parse(JSON.stringify(colValue));
    if (filterData['是否默认当前时间'][item[defaultTime].substring(2, 38)] == '是') {
      let value = '';
      if (type == '日期选择') {
        value = dayjs().format('YYYY-MM-DD');
      } else {
        value = dayjs().format('HH:mm');
      }
      newData[name] = value;
      setColValue(newData);
      return value;
    } else {
      return null;
    }
  }
  // 将timeString转换为TimePicker需要的value格式
  const parseTimeStringToValue = (timeString) => {
    if (!timeString) return [null, null];
    const [startTimeStr, endTimeStr] = timeString.split(' - ');
    const startTime = dayjs(startTimeStr, 'HH:mm')
    const endTime = dayjs(endTimeStr, 'HH:mm')
    return [startTime, endTime];
  }
  // 将timeString转换为TimePicker需要的value格式
  const parseDateTimeStringToValue = (timeString) => {
    if (!timeString) return [null, null];
    const [startTimeStr, endTimeStr] = timeString.split(' - ');
    const startTime = dayjs(startTimeStr, 'YYYY-MM-DD HH:mm')
    const endTime = dayjs(endTimeStr, 'YYYY-MM-DD HH:mm')
    return [startTime, endTime];
  }

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ width: '100%', height: '100%' }}>
        <div style={{ width: '100%', display: 'flex', height: '40px' }}>
          <div style={{ width: 'calc(100% - 120px)', display: 'flex', height: '100%' }}>
            {
              titleText.map((item, i) => {
                return (
                  <div style={{ margin: '11px 0px 9px 20px', lineHeight: '20px', fontSize: '13px', display: 'flex' }}>
                    <div style={{ color: '#757575' }}>
                      {item}：
                    </div>
                    <div style={{ color: '#333333', fontWeight: 'bold' }}>
                      {titleValue[i]}
                    </div>
                  </div>
                );
              })
            }
          </div>
          <div style={{ width: '120px', display: 'flex', height: '100%' }}>
            <Button style={{ margin: '5px 8px 5px 0px', height: '30px' }} onClick={() => showAddModal()}>
              <PlusOutlined />
              新建记录
            </Button>
          </div>
        </div>
        <div style={{ width: '100%', height: 'calc(100% - 40px)', backgroundColor: '#FFFFFF' }}>
          <Table
            className="yl-table"
            columns={formCol}
            dataSource={formData}
            pagination={false}
            bordered
            size='small'
            scroll={{
              x: 'calc(100% - 500px)',
              y: 'calc(100vh - 80px)',
            }}
          />
        </div>
        <Modal
          title={addOrUpdate}
          className="yl-modal"
          open={open}
          maskClosable={false}
          confirmLoading={confirmLoading}
          onCancel={handleCancel}
          footer={addOrUpdate == '新建记录' || addOrUpdate == '复制记录' ? [
            oneClickGet && <Button loading={oneClickLoading} key='get' onClick={handleGetOneClick}>一键获取</Button>,
            <Button key='cancel' onClick={handleCancel} >取消</Button>,
            <Button key='continue' onClick={addAndContinue}>确定并继续</Button>,
            <Button key='confirm' type="primary" onClick={handleOk}>确定</Button>
          ] : [
            oneClickGet && <Button loading={oneClickLoading} key='get' onClick={handleGetOneClick}>一键获取</Button>,
            <Button key='cancel' onClick={handleCancel} >取消</Button>,
            <Button key='confirm' type="primary" onClick={handleOk}>确定</Button>
          ]}
        >
          {contextHolder}
          {
            subTable.map((item, i) => {
              return (
                <div style={{ width: '100%', padding: '5px 8px 0 8px' }}>
                  {
                    !isPutPight && (
                      <p>{formCol[i + 1].title}</p>
                    )
                  }
                  {
                    isPutPight && formCol[i + 1].title in below && (
                      <p>{formCol[i + 1].title}</p>
                    )
                  }

                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '手动输入' && !isPutPight && (
                      <Input style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        value={colValue[formCol[i + 1].title]}
                        onChange={e => {
                          changeColValue(formCol[i + 1].title, e.target.value);
                        }} />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '换行输入' && !isPutPight && (
                      <Input.TextArea style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        placeholder={'换行输入'}
                        value={colValue[formCol[i + 1].title]}
                        onChange={(e) => {
                          changeColValue(formCol[i + 1].title, e.target.value);
                        }}
                        autoSize={{
                          minRows: 3,
                          maxRows: 3,
                        }}
                      />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '公式计算' && !isPutPight && (
                      <Space.Compact style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}>
                        <Input
                          value={colValue[formCol[i + 1].title]}
                          onChange={e => {
                            changeColValue(formCol[i + 1].title, e.target.value);
                          }} />
                        <Button style={{ height: '30px' }}
                          onClick={() => getCount(formCol[i + 1].title)}>
                          计算
                        </Button>
                      </Space.Compact>
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '设备获取' && !isPutPight && (
                      <div>
                        <Space.Compact style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}>
                          <Input
                            style={{ color: result[formCol[i + 1].title] == '不合格' ? 'red' : 'black' }}
                            value={colValue[formCol[i + 1].title]}
                            onChange={e => {
                              changeColValueByNumber(item, formCol[i + 1].title, e.target.value)
                            }}
                            onBlur={e => { reserveDecimal(item, formCol[i + 1].title, e.target.value); }}
                          />
                          <Button style={{ height: '30px' }}
                            onClick={() => getDataByTag(item, formCol[i + 1].title, item[decimalPlaces])}>
                            获取
                          </Button>
                        </Space.Compact>
                      </div>
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '设备获取' && isPutPight && formCol[i + 1].title in below && (
                      <div>
                        <Space.Compact style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}>
                          <Input
                            style={{ color: result[formCol[i + 1].title] == '不合格' ? 'red' : 'black' }}
                            value={colValue[formCol[i + 1].title]}
                            onChange={e => {
                              changeColValueByNumber(item, formCol[i + 1].title, e.target.value)
                            }}
                            onBlur={e => { reserveDecimal(item, formCol[i + 1].title, e.target.value); }}
                          />
                          <Button style={{ height: '30px' }}
                            onClick={() => getDataByTag(item, formCol[i + 1].title, item[decimalPlaces])}>
                            获取
                          </Button>
                        </Space.Compact>
                        <Input.TextArea style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          placeholder={formCol[i + 1].title + '字段纠偏措施'}
                          value={below[formCol[i + 1].title]}
                          onChange={(e) => {
                            let newdata = JSON.parse(JSON.stringify(below));
                            let newdatabak = JSON.parse(JSON.stringify(belowBak));
                            newdata[formCol[i + 1].title] = e.target.value;
                            newdatabak[formCol[i + 1].title] = e.target.value;
                            setBelow(newdata);
                            setBelowBak(newdatabak);
                          }}
                          autoSize={{
                            minRows: 3,
                            maxRows: 3,
                          }}
                        />
                      </div>
                    )
                  }

                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == 'api获取' && !isPutPight && (
                      <Space.Compact style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}>
                        <Input
                          value={colValue[formCol[i + 1].title]}
                          onChange={e => {
                            changeColValue(formCol[i + 1].title, e.target.value);
                          }}
                        />
                        <Button style={{ height: '30px' }}
                          onClick={() => getDataByApi(item[apiUrl], item[apiParam], formCol[i + 1].title)}>
                          获取
                        </Button>
                      </Space.Compact>
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '日期选择' && !isPutPight && (
                      <DatePicker style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        value={colValue[formCol[i + 1].title] != '' ? dayjs(colValue[formCol[i + 1].title], 'YYYY-MM-DD') : getDefaultTime('日期选择', item, formCol[i + 1].title)}
                        onChange={(data, dateString) => {
                          changeColValue(formCol[i + 1].title, dateString);
                        }} />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '时间选择' && !isPutPight && (
                      <TimePicker style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        value={colValue[formCol[i + 1].title] != '' ? dayjs(colValue[formCol[i + 1].title], 'HH:mm') : getDefaultTime('时间选择', item, formCol[i + 1].title)}
                        format={'HH:mm'}
                        onChange={(time, timeString) => {
                          changeColValue(formCol[i + 1].title, timeString);
                        }} />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '时间段选择' && !isPutPight && (
                      <TimePicker.RangePicker style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        value={parseTimeStringToValue(colValue[formCol[i + 1].title])}
                        format={'HH:mm'}
                        onChange={(time, timeString) => {
                          let formatStr = null;
                          if (timeString[0] && timeString[1]) {
                            formatStr = timeString[0] + ' - ' + timeString[1]
                          }

                          changeColValue(formCol[i + 1].title, formatStr);
                        }}
                      />
                    )
                  }{
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '日期时间选择' && !isPutPight && (
                      <DatePicker style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                      value={colValue[formCol[i + 1].title] != '' ? dayjs(colValue[formCol[i + 1].title], 'YYYY-MM-DD HH:mm') : null}
                      showTime={'HH:mm'} format={'YYYY-MM-DD HH:mm'}
                        onChange={(time, dateString) => {
                          changeColValue(formCol[i + 1].title, dateString);
                        }}
                      />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '日期时间段选择' && !isPutPight && (
                      <DatePicker.RangePicker style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        value={parseDateTimeStringToValue(colValue[formCol[i + 1].title])}
                        showTime={{ format: 'HH:mm' }}
                        format={'YYYY-MM-DD HH:mm'}
                        onChange={(time, timeString) => {
                          let formatStr = null;
                          if (timeString[0] && timeString[1]) {
                            formatStr = timeString[0] + ' - ' + timeString[1]
                          }
                          changeColValue(formCol[i + 1].title, formatStr);
                        }}
                      />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '下拉选择' && !isPutPight && (
                      <Select style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        options={getSelectOption(filterData['下拉文本信息'][item[selectValue].substring(2, 38)])}
                        value={colValue[formCol[i + 1].title]}
                        onChange={value => {
                          changeColValue(formCol[i + 1].title, value);
                        }} />
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '数字输入' && !isPutPight && (
                      <div>
                        <InputNumber style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          className={result[formCol[i + 1].title] === '不合格' ? 'yl-errorNumber' : 'yl-normalNumber'}
                          value={colValue[formCol[i + 1].title]}
                          step={null}
                          onChange={value => {
                            changeColValueByNumber(item, formCol[i + 1].title, value);
                          }}
                          onBlur={e => { reserveDecimal(item, formCol[i + 1].title, e.target.value); }}
                        />
                      </div>
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '数字输入' && isPutPight && formCol[i + 1].title in below && (
                      <div>
                        <InputNumber style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          className={result[formCol[i + 1].title] === '不合格' ? 'yl-errorNumber' : 'yl-normalNumber'}
                          value={colValue[formCol[i + 1].title]}
                          step={null}
                          onChange={value => {
                            changeColValueByNumber(item, formCol[i + 1].title, value);
                          }}
                          onBlur={e => { reserveDecimal(item, formCol[i + 1].title, e.target.value); }}
                        />
                        <Input.TextArea style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          placeholder={formCol[i + 1].title + '字段纠偏措施'}
                          value={below[formCol[i + 1].title]}
                          onChange={(e) => {
                            let newdata = JSON.parse(JSON.stringify(below));
                            let newdatabak = JSON.parse(JSON.stringify(belowBak));
                            newdata[formCol[i + 1].title] = e.target.value;
                            newdatabak[formCol[i + 1].title] = e.target.value;
                            setBelow(newdata);
                            setBelowBak(newdatabak);
                          }}
                          autoSize={{
                            minRows: 3,
                            maxRows: 3,
                          }}
                        />
                      </div>
                    )
                  }

                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '复选框选择' && filterData['复选框类型'][item[checkType].substring(2, 38)] == '多选' && !isPutPight && (
                      <Checkbox.Group style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                        value={colValue[formCol[i + 1].title] != '' ? colValue[formCol[i + 1].title].split('，') : []}
                        onChange={(checkedValues) => {
                          changeCheckValue(formCol[i + 1].title, checkedValues, '多选');
                        }}>
                        {
                          getMulCheckOption(filterData['复选框文本信息'][item[checkValue].substring(2, 38)])
                        }
                      </Checkbox.Group>
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '复选框选择' && filterData['复选框类型'][item[checkType].substring(2, 38)] == '单选' && !isPutPight && (
                      <div>
                        <Checkbox.Group style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          value={colValue[formCol[i + 1].title] != '' ? colValue[formCol[i + 1].title].split('，') : []}
                          onChange={(value) => {
                            changeCheckValue(formCol[i + 1].title, value, '单选');
                          }}
                        >
                          {
                            getSinCheckOption(filterData['复选框文本信息'][item[checkValue].substring(2, 38)])
                          }
                        </Checkbox.Group>
                      </div>
                    )
                  }
                  {
                    filterData['获取方式'][item[valueType].substring(2, 38)] == '复选框选择' && filterData['复选框类型'][item[checkType].substring(2, 38)] == '单选' && isPutPight && formCol[i + 1].title in below && (
                      <div>
                        <Checkbox.Group style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          value={colValue[formCol[i + 1].title] != '' ? colValue[formCol[i + 1].title].split('，') : null}
                          onChange={(value) => {
                            changeCheckValue(formCol[i + 1].title, value, '单选');
                          }}>
                          {
                            getSinCheckOption(filterData['复选框文本信息'][item[checkValue].substring(2, 38)])
                          }
                        </Checkbox.Group>
                        <Input.TextArea style={{ width: '100%', height: '30px', float: 'left', marginBottom: '7px' }}
                          placeholder={formCol[i + 1].title + '字段纠偏措施'}
                          value={below[formCol[i + 1].title]}
                          onChange={(e) => {
                            let newdata = JSON.parse(JSON.stringify(below));
                            let newdatabak = JSON.parse(JSON.stringify(belowBak));
                            newdata[formCol[i + 1].title] = e.target.value;
                            newdatabak[formCol[i + 1].title] = e.target.value;
                            setBelow(newdata);
                            setBelowBak(newdatabak);
                          }}
                          autoSize={{
                            minRows: 3,
                            maxRows: 3,
                          }}
                        />
                      </div>
                    )
                  }
                </div>
              );
            })
          }
        </Modal>
        <Modal className="yl-errorModai" title="纠偏措施" open={isModalOpen} onCancel={() => { setIsModalOpen(false) }} footer={null} >
          {/* <Descriptions items={belowModal} column={1} /> */}
          {
            belowModal.map((item) => {
              return (
                <div style={{ width: '100%', padding: '5px 8px 0 8px' }}>
                  <p style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{item.label}：<span style={{ color: 'black' }}>{item.children}</span></p>
                </div>
              );
            })
          }
        </Modal>
      </div>
    </ConfigProvider>
  );
}
